import { useAuth } from '@/features/auth/hooks';

/**
 * Maps a user's language code to the API language code format
 * @param userLanguage The user's language code (e.g., 'pt', 'en')
 * @returns The corresponding API language code (e.g., 'PT_BR', 'EN_US')
 */
const LANGUAGE_MAP: Record<string, string> = {
  en: 'EN_US',
  pt: 'PT_BR',
  es: 'ES_ES',
  fr: 'FR_FR',
  de: 'DE_DE',
  it: 'IT_IT',
  // Add more language mappings as needed
};

// Default fallback language code
const DEFAULT_LANGUAGE = 'en';

// Default fallback API language code
const DEFAULT_API_LANGUAGE = 'EN_US';

/**
 * Detects the system language from browser or environment
 * @returns ISO 639-1 language code (e.g., 'en', 'pt')
 */
function detectSystemLanguage(): string {
  try {
    // Try to get browser language
    if (typeof window !== 'undefined' && window.navigator) {
      const nav = window.navigator;
      const browserLang = nav.language || 
                        (nav as any).userLanguage ||
                        (nav as any).browserLanguage;
      
      if (browserLang) {
        // Extract primary language code (e.g., 'en' from 'en-US')
        return browserLang.split('-')[0].toLowerCase();
      }
    }

    // Try to get from environment variables (Node.js/SSR)
    if (typeof process !== 'undefined' && process.env) {
      const env = process.env as NodeJS.ProcessEnv;
      const envVars = ['LANG', 'LANGUAGE', 'LC_ALL', 'LC_MESSAGES'] as const;
      
      for (const envVar of envVars) {
        try {
          const value = env[envVar as keyof NodeJS.ProcessEnv];
          if (value && typeof value === 'string' && value.trim().length > 0) {
            const parts = value.split('_');
            if (parts[0]) {
              const langPart = parts[0].split('.')[0];
              if (langPart) {
                return langPart.toLowerCase();
              }
            }
          }
        } catch (error) {
          console.warn(`Error reading environment variable ${envVar}:`, error);
          continue;
        }
      }
    }
  } catch (error) {
    console.warn('Error detecting system language, using default', error);
  }
  
  return DEFAULT_LANGUAGE;
}

/**
 * Gets the API language code from a user language code
 * @param userLanguage - ISO 639-1 language code (e.g., 'en', 'pt')
 * @returns API language code (e.g., 'EN_US', 'PT_BR')
 */
export function getApiLanguage(userLanguage?: string | null): string {
  if (!userLanguage) return DEFAULT_API_LANGUAGE;
  return LANGUAGE_MAP[userLanguage.toLowerCase()] || DEFAULT_API_LANGUAGE;
}

/**
 * Gets the user's language from auth context with fallback to system language and then default
 * @returns ISO 639-1 language code (e.g., 'en', 'pt')
 */
export function useUserLanguage(): string {
  const { profile } = useAuth();
  // Try in order: 1. User profile language, 2. System language, 3. Hardcoded default
  return profile?.language || detectSystemLanguage() || DEFAULT_LANGUAGE;
}

/**
 * Gets the API language code using the user's preferred language with fallbacks
 * @returns API language code (e.g., 'EN_US', 'PT_BR')
 */
export function useApiLanguage(): string {
  const userLanguage = useUserLanguage();
  return getApiLanguage(userLanguage);
}
