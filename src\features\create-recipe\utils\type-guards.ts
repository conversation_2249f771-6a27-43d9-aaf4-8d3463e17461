/**
 * @fileoverview Type guard functions for the recipe types.
 */

import { EnrichedEssentialOil, EssentialOil } from '../types/recipe.types';

/**
 * Type guard to check if an oil is an enriched oil with safety data
 */
export function isEnrichedOil(oil: EssentialOil | EnrichedEssentialOil): oil is EnrichedEssentialOil {
  return (
    oil !== undefined &&
    typeof oil === 'object' &&
    'isEnriched' in oil &&
    oil.isEnriched === true
  );
} 