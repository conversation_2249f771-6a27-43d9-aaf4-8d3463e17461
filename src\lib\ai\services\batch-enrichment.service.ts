/**
 * @fileoverview Batch oil enrichment service for efficient safety data retrieval
 * Replaces individual AI agent calls with batch processing for better performance
 */

import { createClient } from '@/lib/supabase/server';
import { getBatchEmbeddingsService } from './unified-embeddings.service';

/**
 * Input oil data from AI agent (suggested-oils.yaml output)
 */
export interface SuggestedOilData {
  oil_id: string;
  name_english: string;
  name_botanical: string;
  name_localized: string;
  match_rationale_localized: string;
  relevancy_to_property_score: number;
  isEnriched?: boolean;
}

/**
 * Safety data structure with IDs
 */
export interface OilSafetyData {
  internal_use: any;
  dilution: any;
  phototoxicity: any;
  pregnancy_nursing: any;
  child_safety: any;
  // Safety IDs from Supabase
  internal_use_id?: string;
  dilution_id?: string;
  phototoxicity_id?: string;
  pregnancy_nursing_ids?: string[];
  child_safety_ids?: string[];
}

/**
 * Enriched oil data with safety information from vector search
 */
export interface EnrichedOilData extends SuggestedOilData {
  // Safety data from vector search
  name_scientific?: string;
  safety?: OilSafetyData;
  
  // Enrichment metadata
  enrichment_status: 'enriched' | 'not_found' | 'discarded';
  botanical_mismatch?: boolean;
  similarity_score?: number;
  search_query?: string;
  enrichment_timestamp?: string;
  isEnriched: boolean;
}

/**
 * Batch enrichment response
 */
export interface BatchEnrichmentResponse {
  enriched_oils: EnrichedOilData[];
  total_input: number;
  total_enriched: number;
  total_not_found: number;
  total_discarded: number;
  processing_time_ms: number;
}

/**
 * Batch oil enrichment service
 * @class BatchEnrichmentService
 */
export class BatchEnrichmentService {
  private batchEmbeddingService: ReturnType<typeof getBatchEmbeddingsService>;
  
  constructor() {
    this.batchEmbeddingService = getBatchEmbeddingsService();
    console.log('🔧 [BatchEnrichmentService] Initialized with BATCH embeddings service');
  }

  /**
   * Enriches a batch of suggested oils with safety data from vector search
   * @param suggestedOils Array of oils from AI agent output
   * @returns Promise<BatchEnrichmentResponse>
   */
  async batchEnrichOils(suggestedOils: SuggestedOilData[]): Promise<BatchEnrichmentResponse> {
    const startTime = performance.now();
    
    console.log('ACTION: batchEnrichOils');
    console.log('PAYLOAD:', suggestedOils);
    console.log(`🔍 [BatchEnrichmentService] Starting batch enrichment for ${suggestedOils.length} oils`);
    
    if (suggestedOils.length === 0) {
      return {
        enriched_oils: [],
        total_input: 0,
        total_enriched: 0,
        total_not_found: 0,
        total_discarded: 0,
        processing_time_ms: performance.now() - startTime
      };
    }

    try {
      // Step 1: Format search queries - ${name_english} - ${name_botanical}
      const searchQueries = suggestedOils.map(oil => 
        `${oil.name_english} - ${oil.name_botanical}`.trim()
      );
      
      console.log('📤 [BatchEnrichmentService] Search queries formatted:', searchQueries);

      // Step 2: Generate batch embeddings using a single API call
      console.log('🔍 [BatchEnrichmentService] Creating batch embeddings...');
      const embeddingsResults = await this.batchEmbeddingService.createBatchEmbeddings({
        texts: searchQueries
      });
      
      console.log('📊 [BatchEnrichmentService] Embeddings created:', embeddingsResults.length);

      // Step 3: Perform batch vector search
      const supabase = await createClient();
      
      // Convert embeddings to JSON strings for vector[] type
      const embeddings = embeddingsResults.map(result => JSON.stringify(result.embedding));
      
      const { data: searchResults, error } = await supabase.rpc('batch_similarity_search_with_ids', {
        query_embeddings: embeddings,
        match_threshold: 0.5,
        match_count: 3,
      });

      if (error) {
        console.error('❌ [BatchEnrichmentService] Supabase RPC error:', error);
        throw new Error('Vector search failed - database error');
      }

      console.log('✅ [BatchEnrichmentService] Vector search completed:', searchResults?.length || 0, 'results');

      // Step 4: Process results and apply botanical validation
      const enrichedOils = await this.processSearchResults(suggestedOils, searchQueries, searchResults || []);
      
      const endTime = performance.now();
      const stats = this.calculateStats(enrichedOils);
      
      console.log('📊 [BatchEnrichmentService] Enrichment completed:', {
        total_input: suggestedOils.length,
        ...stats,
        processing_time_ms: Math.round(endTime - startTime)
      });

      return {
        enriched_oils: enrichedOils,
        total_input: suggestedOils.length,
        ...stats,
        processing_time_ms: Math.round(endTime - startTime)
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ [BatchEnrichmentService] Batch enrichment failed:', errorMessage);
      // Throw descriptive errors that will be handled by the API route's error handling
      throw new Error(`Oil enrichment failed: ${errorMessage}`);
    }
  }

  /**
   * Process search results and apply botanical name validation
   */
  private async processSearchResults(
    suggestedOils: SuggestedOilData[],
    searchQueries: string[],
    searchResults: any[]
  ): Promise<EnrichedOilData[]> {
    console.log('ACTION: processSearchResults');
    console.log('PAYLOAD:', { suggestedOils, searchQueries, searchResults });
    const enrichedOils: EnrichedOilData[] = [];
    
    // Group results by embedding_index
    const resultsByIndex = searchResults.reduce((acc: any, result: any) => {
      const index = result.embedding_index;
      if (!acc[index]) acc[index] = [];
      acc[index].push(result);
      return acc;
    }, {});

    // Process each oil
    for (let i = 0; i < suggestedOils.length; i++) {
      const oil = suggestedOils[i];
      const embeddingIndex = i + 1; // Supabase function uses 1-based indexing
      const results = resultsByIndex[embeddingIndex] || [];
      const searchQuery = searchQueries[i];

      // Linter fix: ensure oil object is valid before proceeding
      if (!oil || !oil.name_english || !oil.name_botanical) {
        console.warn('⏩ [BatchEnrichmentService] Skipping invalid oil data:', oil);
        continue;
      }

      if (results.length === 0) {
        // No results found
        console.log(`🟡 [BatchEnrichmentService] NOT FOUND: "${oil.name_english}" - No vector search results.`);
        const enrichedOil: EnrichedOilData = {
          ...oil,
          enrichment_status: 'not_found',
          search_query: searchQuery,
          enrichment_timestamp: new Date().toISOString(),
          isEnriched: false
        };
        enrichedOils.push(enrichedOil);
        continue;
      }

      // Get best match (first result, already sorted by similarity)
      const bestMatch = results[0];
      const similarity = bestMatch.similarity;

      // Check if botanical name matches
      const botanicalMismatch = this.checkBotanicalMismatch(oil.name_botanical, bestMatch.name_scientific);

      // Discard if similarity is too low and botanical name doesn't match
      if (similarity < 0.65 && botanicalMismatch) {
        console.log(`🔴 [BatchEnrichmentService] DISCARDED: "${oil.name_english}" - Low similarity (${similarity.toFixed(3)}) and botanical mismatch.`);
        const enrichedOil: EnrichedOilData = {
          ...oil,
          enrichment_status: 'discarded',
          botanical_mismatch: true,
          similarity_score: similarity,
          search_query: searchQuery,
          enrichment_timestamp: new Date().toISOString(),
          isEnriched: false
        };
        enrichedOils.push(enrichedOil);
        continue;
      }

      // Create enriched oil with safety data properly structured
      const safety: OilSafetyData = {
        internal_use: bestMatch.internal_use,
        dilution: bestMatch.dilution,
        phototoxicity: bestMatch.phototoxicity,
        pregnancy_nursing: bestMatch.pregnancy_nursing_safety,
        child_safety: bestMatch.child_safety
      };

      // Extract safety IDs
      safety.internal_use_id = bestMatch.internal_use_status_id;
      safety.dilution_id = bestMatch.dilution_recommendation_id;
      safety.phototoxicity_id = bestMatch.phototoxicity_status_id;
      
      // Extract pregnancy nursing IDs from the JSON array
      if (Array.isArray(bestMatch.pregnancy_nursing_safety)) {
        safety.pregnancy_nursing_ids = bestMatch.pregnancy_nursing_safety.map((item: any) => item.id).filter(Boolean);
      }
      
      // Extract child safety IDs from the JSON array
      if (Array.isArray(bestMatch.child_safety)) {
        safety.child_safety_ids = bestMatch.child_safety.map((item: any) => item.age_range_id).filter(Boolean);
      }

      // Check if safety data is actually present (not just empty fields)
      const hasSafetyData = Object.values(safety).some(value => value !== null && value !== undefined && value !== '');

      // For enriched/valid oils, set oil_id to the official Supabase ID
      const enrichedOil: EnrichedOilData = {
        ...oil,
        oil_id: bestMatch.id, // <-- Use the official Supabase oil ID
        name_scientific: bestMatch.name_scientific,
        safety: hasSafetyData ? safety : undefined,
        enrichment_status: hasSafetyData ? 'enriched' : 'not_found',
        botanical_mismatch: botanicalMismatch,
        similarity_score: similarity,
        search_query: searchQuery,
        enrichment_timestamp: new Date().toISOString(),
        isEnriched: hasSafetyData
      };

      if (hasSafetyData) {
        console.log(`✅ [BatchEnrichmentService] ENRICHED: "${oil.name_english}" - Similarity: ${similarity.toFixed(3)}, Safety data present`);
      } else {
        console.log(`🟡 [BatchEnrichmentService] PARTIAL: "${oil.name_english}" - Found match but no safety data`);
      }
      enrichedOils.push(enrichedOil);
    }

    return enrichedOils;
  }

  /**
   * Calculate enrichment statistics
   */
  private calculateStats(enrichedOils: EnrichedOilData[]) {
    return {
      total_enriched: enrichedOils.filter(o => o.enrichment_status === 'enriched').length,
      total_not_found: enrichedOils.filter(o => o.enrichment_status === 'not_found').length,
      total_discarded: enrichedOils.filter(o => o.enrichment_status === 'discarded').length,
    };
  }

  private checkBotanicalMismatch(searchedBotanical: string, foundBotanical: string | undefined): boolean {
    if (!foundBotanical) return true; // If no scientific name is found, assume mismatch
    const trimmedSearched = searchedBotanical.trim().toLowerCase();
    const trimmedFound = foundBotanical.trim().toLowerCase();
    return trimmedSearched !== trimmedFound;
  }
}

/**
 * Singleton instance of the BatchEnrichmentService
 */
let serviceInstance: BatchEnrichmentService | null = null;

export function getBatchEnrichmentService(): BatchEnrichmentService {
  if (!serviceInstance) {
    serviceInstance = new BatchEnrichmentService();
  }
  return serviceInstance;
}

export async function batchEnrichOils(suggestedOils: SuggestedOilData[]): Promise<BatchEnrichmentResponse> {
  const service = getBatchEnrichmentService();
  return service.batchEnrichOils(suggestedOils);
}