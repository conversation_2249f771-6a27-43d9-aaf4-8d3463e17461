### Category 1: User & Condition Analysis (The "Why")

These items focus on understanding the person and their health needs before any oils are selected.

**1. Specific Health Concern**
*   **Description:** The primary reason for creating the protocol. All documents are centered around a specific health issue.
*   **Details:** The concern is consistently "chronic headaches worsened by stress and lack of sleep."

**2. User Demographics (Specifically Age)**
*   **Description:** Identifying the user's age is a critical first step for determining safety and dilution.
*   **Details:** The specific age of a "3-year-old child" is a foundational data point in all practical protocols and a key field in the frameworks.

**3. Root Cause Analysis**
*   **Description:** Moving beyond symptoms to identify the underlying triggers, such as stress or sleep issues.
*   **Details:** The primary causes are consistently identified as "Stress/anxiety" and "Sleep deprivation/lack of sleep."

### Category 2: Ingredient Selection & Formulation (The "What")

These items detail the specific ingredients of the recipe and the rationale behind their selection.

**4. Specific Essential Oil Recommendations**
*   **Description:** A list of the exact essential oils to be used in the recipes.
*   **Details:** **Lavender (Lavanda)** and **Copaiba** are the most consistently recommended oils across the practical protocols. Frankincense (Olíbano) and Peppermint (Hortelã-pimenta) are also frequently mentioned.

**5. Rationale for Oil Selection (Therapeutic Properties)**
*   **Description:** Explaining *why* a specific oil was chosen, linking it back to the user's needs (e.g., "calming," "analgesic").
*   **Details:** The `gemini` and `aistudio` files provide a "Racional" for each oil in the recipe. The framework files (`overview`, `prd`) mandate defining the "Therapeutic Properties Needed."

**6. Precise Recipe Formulation**
*   **Description:** The exact "recipe" including the number of drops for each essential oil, the type of carrier oil, and the total volume of the container.
*   **Details:** Recipes consistently specify drop counts (e.g., "2 drops"), a carrier oil (e.g., "óleo de coco fracionado"), and a final volume (e.g., "10ml").

**7. Dilution Rate Calculation**
*   **Description:** Specifying the concentration of essential oils in the carrier oil, a critical safety parameter, especially for children.
*   **Details:** The concept of a safe dilution percentage (e.g., 0.5-1%) is explicitly mentioned and calculated in all protocols and frameworks.

### Category 3: Usage Protocol (The "How")

These items provide actionable instructions on how and when to use the prepared recipe.

**8. Application Method**
*   **Description:** Clear instructions on how to apply the blend to the body or use it in the environment.
*   **Details:** The most common methods are **Topical Application** (e.g., roll-on, massage on feet, neck, temples) and **Aromatic Use / Diffusion**.

**9. Usage Timing & Schedule**
*   **Description:** A structured schedule for when to use the recipes, often broken down by time of day or week.
*   **Details:** A **Daily Routine** (Morning/Tarde/Night) is a universal concept. A **Weekly Schedule** is also a strong common theme, appearing in `claude_protocol`, `gemini_protocol`, and `aistudio_protocol`.

**10. Container Recommendations**
*   **Description:** Specifying the type of container to use for the recipe, which is linked to the application method.
*   **Details:** The **Roll-On bottle** (for topical use) is the most frequently mentioned container.

### Category 4: Safety & Monitoring (The "Rules")

This is the most critical and consistent category, emphasizing responsible use.

**11. Overall Safety Guidelines & Precautions**
*   **Description:** A dedicated section outlining all safety warnings and rules for using the protocol.
*   **Details:** This is a non-negotiable component of every file. Specific common safety points include:
    *   **Age-Specific Warnings:** Explicitly stating rules for children. (5 files)
    *   **Restricted/Avoided Oils:** Warning against using certain oils for the target user (e.g., Peppermint, Rosemary for a young child). (4 files)
    *   **Phototoxicity Warnings:** Advising to avoid sun exposure after applying certain oils (e.g., citrus oils). (4 files)
    *   **Patch Test Protocol:** Instructing the user to test for skin sensitivity before full use. (4 files)
    *   **Medical Disclaimer / Consult a Professional:** Advising that the protocol is not a substitute for medical advice and to consult a doctor. (4 files)

**12. Progress Tracking & Evaluation**
*   **Description:** A framework for monitoring the effectiveness of the protocol and making adjustments.
*   **Details:** This includes tracking metrics (e.g., headache intensity), assessing improvement, and having a timeline for re-evaluation.