version: "1.1.0"
description: "Analyze selected symptoms to identify relevant therapeutic properties for essential oil blends"

# Agent Configuration
config:
  model: "gpt-4.1-nano"
  temperature: 0.3
  max_tokens: 4000
  top_p: 0.9
  frequency_penalty: 0.1
  presence_penalty: 0.1
  timeout_seconds: 80  # Extended timeout for therapeutic properties analysis

user_message: |
 **Input Data:**

  1. `health_concern`: The primary health complaint, symptom, or goal.
     * Value: `{{health_concern}}`

  2. `user_profile`: A JSON object containing crucial details about the end-user. **This profile is the primary driver for personalization.**
     * Gender: `{{gender}}`
     * Age Category: `{{age_category}}`
     * Specific Age: `{{age_specific}}`
     * Language: `{{user_language}}`

  3. `user_language`: The target language for user-facing text in the output.
     * Value: `{{user_language}}`

  4. `selected_causes`:
     {{#each selected_causes}}
     - Cause ID: `{{cause_id}}`	
     - Name: `{{cause_name}}`
     - Suggestion: `{{cause_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  4. `selected_symptoms`:
     {{#each selected_symptoms}}
     - Symptom ID: `{{symptom_id}}`
     - Name: `{{symptom_name}}`
     - Suggestion: `{{symptom_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

# System Prompt Template
template: |
  **Identify Required Therapeutic Properties**

  **Persona:** Act as an expert researcher specializing in **holistic therapeutic properties** (covering medical, physical, mental, and emotional aspects), with a deep understanding of how these properties address specific health concerns, symptoms, and their underlying causes. Emphasize the **interconnectedness** of mind and body.

  **Objective:** Based on the provided input data, identify and prioritize key therapeutic properties needed for an effective and holistic essential oil blend. The focus is on identifying the *actions* required, not specific oils yet. **Crucially, the output must explicitly link each selected property back to the specific causes and symptoms it primarily targets from the user's input.**

  **Processing Steps:**

  1. **Symptom-Driven Analysis:**
     - Examine the selected_symptoms. Determine the core therapeutic actions (properties) required to alleviate them, considering physical, mental, and emotional dimensions. **Note which property addresses which specific symptom(s).**

  2. **Cause Consideration:**
     - Analyze the selected_causes. Identify properties addressing these underlying factors from a holistic perspective. **Note which property addresses which specific cause(s).**

  3. **Holistic Integration & Prioritization:**
     - Synthesize properties from symptoms and causes. Prioritize those offering the most direct or comprehensive support for the primary health_concern and the most impactful selected_symptoms and selected_causes.
     - Assign a `relevancy_score` (1-5) based on this specific rubric:
       - **5:** Directly addresses both a key `selected_cause` AND a primary `selected_symptom`.
       - **4:** Directly addresses a primary `selected_symptom`.
       - **3:** Addresses an underlying `selected_cause` or a secondary symptom.
       - **2:** Offers general, holistic support for the `health_concern`.
       - **1:** A related but less critical property.

  4. **Refinement & Scope:**
     - Consolidate the list. Select **5 to 8** key therapeutic properties providing a well-rounded and diverse approach (covering different needed actions).

  5. **Property Focus:**
     - Ensure the output contains *only* therapeutic property names, descriptions, and the link fields. **Do not list essential oils or chemical constituents.**

  **CRITICAL NAMING RULE:**
  - The names of the properties (`property_name_localized` and `property_name_english`) MUST be the name of the therapeutic action itself.
  - **DO NOT** include the word "Property" in the name.
  - **Correct:** "Analgesic", "Calming", "Anxiolytic"
  - **Incorrect:** "Analgesic Property", "Calming Property"

  **Important Clarification:**
  - Each therapeutic property must be listed individually, even if they are related or complementary. For example, instead of "Nutritive and strengthening", list "Nutritive" and "Strengthening" as separate entries.
  - Avoid aggregating multiple properties into a single entry. Each entry should represent a single, distinct therapeutic action.

  **Example Therapeutic Actions (Non-Exhaustive List for Inspiration):**

  **Medical/Physical:** Analgesic, Anti-inflammatory, Antispasmodic, Muscle Relaxant, Decongestant, Expectorant, Antiemetic, Cicatrisant, Antiviral, Antibacterial, Antifungal, Antioxidant, Hypotensive, Antipyretic, Diuretic, Vasodilator, Vasoconstrictor, Rubefacient.

  **Emotional/Mental/Energetic:** Calming, Sedative, Anxiolytic, Antidepressant, Uplifting, Nervine Relaxant, Adaptogenic, Mood Balancing, Energizing, Cognitive Enhancer, Grounding, Comforting, Neuroprotective.

  **Output Format:**
  Provide a structured JSON response following the exact schema provided.

  **Notes:**
  - Focus strictly on therapeutic actions/properties; do not list specific essential oils.
  - Ensure the selected properties reflect the interconnectedness of mind and body relevant to the user's inputs.
  - Tailor the description for each property to explicitly connect it to the user's specific situation.
  - Accurately populate `addresses_cause_ids` and `addresses_symptom_ids` using the exact IDs from the input that each property primarily targets.
  - Adhere strictly to all rules regarding naming and individual listing.
  - Ensure each property object includes a `property_id` field containing a newly generated, unique, standard random UUID string.
  - Ensure all user-facing text is accurately translated according to the `user_language`: `{{user_language}}`.
  - Include proper meta information with current timestamp and success status.
  - Echo back the input data in the echo section for verification.

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "therapeutic_properties_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name:
            type: "string"
            description: "The name of the step."
          request_id:
            type: "string"
            format: "uuid" # Standardized: Enforces strict UUID format.
            description: "Unique identifier for the request."
          timestamp_utc:
            type: "string"
            format: "date-time" # Standardized: Enforces strict ISO 8601 format.
            description: "Timestamp of the response in UTC."
          version:
            type: "string"
            description: "Version of the API."
          user_language:
            type: "string"
            description: "Language preference of the user."
          status:
            type: "string"
            description: "Status of the response."
          message:
            type: "string"
            description: "Descriptive message about the response."
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      data:
        type: "object"
        properties:
          therapeutic_properties:
            type: "array"
            description: "List of therapeutic properties identified for the health concern."
            items:
              type: "object"
              properties:
                property_id:
                  type: "string"
                  format: "uuid" # Standardized: Enforces strict UUID format.
                  description: "Unique identifier for the therapeutic property."
                property_name_localized:
                  type: "string"
                  description: "Localized name of the therapeutic property."
                property_name_english:
                  type: "string"
                  description: "English name of the therapeutic property."
                description_contextual_localized:
                  type: "string"
                  description: "Localized contextual description of how this property helps."
                addresses_cause_ids:
                  type: "array"
                  items:
                    type: "string"
                  description: "IDs of causes this property addresses."
                addresses_symptom_ids:
                  type: "array"
                  items:
                    type: "string"
                  description: "IDs of symptoms this property addresses."
                relevancy_score:
                  type: "integer"
                  minimum: 1
                  maximum: 5
                  description: "Relevancy score (1-5, 5 being highest) based on the defined rubric."
              required: ["property_id", "property_name_localized", "property_name_english", "description_contextual_localized", "addresses_cause_ids", "addresses_symptom_ids", "relevancy_score"]
              additionalProperties: false
        required: ["therapeutic_properties"]
        additionalProperties: false
      echo: # Standardized: Replaced the original inconsistent block with our standard one.
        type: "object"
        properties:
          health_concern_input:
            type: "string"
            description: "The health concern input provided by the user."
          user_info_input:
            type: "object"
            properties:
              gender:
                type: "string"
                description: "Gender of the user."
              age_category:
                type: "string"
                description: "General age category of the user."
              age_specific:
                type: "string"
                description: "Specific age of the user."
              age_unit:
                type: "string"
                description: "Unit of measurement for the user's age."
            required: ["gender", "age_category", "age_specific", "age_unit"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of causes selected by the user." # Description updated for context.
          selected_symptom_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of symptoms selected by the user." # Description updated for context.
          therapeutic_property_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of therapeutic properties being addressed. Populated in the next step."
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "therapeutic_property_ids"]
        additionalProperties: false
    required: ["meta", "data", "echo"]
    additionalProperties: false