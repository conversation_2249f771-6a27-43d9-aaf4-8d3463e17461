/**
 * @fileoverview Prompt Manager service for loading and processing YAML prompt configurations
 * for AI streaming integrations. Moved from recipe-wizard to be shared across features.
 */

import * as yaml from 'js-yaml';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Prompt configuration interface
 */
export interface PromptConfig {
  version: string;
  description: string;
  config: {
    model: string;
    temperature: number;
    max_tokens: number;
    [key: string]: any;
  };
  template: string;
  user_message?: string; // Optional user message template
  schema: {
    type: string;
    name?: string;
    schema?: any;
    [key: string]: any;
  };
  timeout_seconds?: number; // Optional timeout in seconds
  [key: string]: any;
}

/**
 * Error class for prompt management operations
 */
export class PromptManagerError extends Error {
  constructor(
    message: string,
    public code: string,
    public promptName?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'PromptManagerError';

    // Maintain proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, PromptManagerError.prototype);
  }
}

/**
 * Template variable substitution interface
 */
interface TemplateVariables {
  [key: string]: any;
}

/**
 * Prompt Manager class for handling YAML prompt configurations
 */
export class PromptManager {
  private static instance: PromptManager;
  private promptCache: Map<string, PromptConfig> = new Map();
  private readonly promptsBasePath: string;

  private constructor() {
    // Set base path for prompts directory - now supports create-recipe
    this.promptsBasePath = path.join(process.cwd(), 'src', 'features', 'create-recipe', 'prompts');
  }

  /**
   * Set custom prompts base path (useful for different features)
   */
  public setPromptsBasePath(basePath: string): void {
    (this as any).promptsBasePath = basePath;
    this.clearCache(); // Clear cache when path changes
  }

  /**
   * Get singleton instance of PromptManager
   */
  public static getInstance(): PromptManager {
    if (!PromptManager.instance) {
      PromptManager.instance = new PromptManager();
    }
    return PromptManager.instance;
  }

  /**
   * Load a YAML prompt configuration file
   */
  public async loadPromptConfig(promptName: string): Promise<PromptConfig> {
    // Check cache first
    if (this.promptCache.has(promptName)) {
      return this.promptCache.get(promptName)!;
    }

    try {
      // Construct the file path
      const filePath = path.join(this.promptsBasePath, `${promptName}.yaml`);
      
      // Debug logging
      console.log(`[Prompt Manager] Loading prompt from: ${filePath}`);
      
      // Check if file exists
      try {
        await fs.access(filePath);
      } catch (accessError) {
        console.error(`[Prompt Manager] File does not exist: ${filePath}`, accessError);
        throw new PromptManagerError(
          `Prompt file not found: ${filePath}`,
          'FILE_NOT_FOUND',
          promptName,
          accessError as Error
        );
      }

      // Read and parse YAML file
      const fileContent = await fs.readFile(filePath, 'utf-8');
      
      if (!fileContent.trim()) {
        throw new PromptManagerError(
          `Prompt file is empty: ${filePath}`,
          'EMPTY_FILE',
          promptName
        );
      }
      
      let promptConfig: PromptConfig;
      try {
        promptConfig = yaml.load(fileContent) as PromptConfig;
      } catch (yamlError) {
        console.error(`[Prompt Manager] YAML parsing error in ${filePath}:`, yamlError);
        throw new PromptManagerError(
          `Invalid YAML in prompt file: ${filePath}`,
          'INVALID_YAML',
          promptName,
          yamlError as Error
        );
      }

      // Validate the loaded configuration
      this.validatePromptConfig(promptConfig, promptName);

      // Cache the configuration
      this.promptCache.set(promptName, promptConfig);
      console.log(`[Prompt Manager] Successfully loaded prompt: ${promptName}`);

      return promptConfig;
    } catch (error) {
      if (error instanceof PromptManagerError) {
        console.error(`[Prompt Manager] Error loading prompt ${promptName}:`, error.message);
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[Prompt Manager] Unexpected error loading prompt ${promptName}:`, errorMessage);
      
      throw new PromptManagerError(
        `Failed to load prompt configuration: ${promptName} - ${errorMessage}`,
        'LOAD_ERROR',
        promptName,
        error as Error
      );
    }
  }

  /**
   * Get nested value from object using dot notation (e.g., "demographics.gender")
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Process template with variable substitution - VERSÃO EXPERT MELHORADA
   */
  public processTemplate(template: string, variables: TemplateVariables): string {
    try {
      let processedTemplate = template;

      // Handle Handlebars-style {{#each array}} loops PRIMEIRO (para contexto correto)
      processedTemplate = processedTemplate.replace(
        /\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g,
        (match, arrayName, loopContent) => {
          if (variables.hasOwnProperty(arrayName) && Array.isArray(variables[arrayName])) {
            const array = variables[arrayName];
            
            // Handle empty arrays
            if (array.length === 0) {
              return '';
            }
            
            return array
              .map((item: any, index: number) => {
                let itemContent = loopContent;
                const isFirst = index === 0;
                const isLast = index === array.length - 1;
                
                // Replace special variables
                itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index));
                itemContent = itemContent.replace(/\{\{@first\}\}/g, String(isFirst));
                itemContent = itemContent.replace(/\{\{@last\}\}/g, String(isLast));
                itemContent = itemContent.replace(/\{\{@key\}\}/g, String(index));
                
                // Handle {{this}} with proper type conversion
                itemContent = itemContent.replace(/\{\{this\}\}/g, 
                  typeof item === 'object' && item !== null ? JSON.stringify(item) : String(item)
                );
                
                // Handle {{#if @first}}...{{/if}} - NOVO
                itemContent = itemContent.replace(
                  /\{\{#if\s+@first\}\}([\s\S]*?)\{\{\/if\}\}/g,
                  (_match: string, content: string) => isFirst ? content : ''
                );
                
                // Handle {{#if @last}}...{{/if}} - NOVO
                itemContent = itemContent.replace(
                  /\{\{#if\s+@last\}\}([\s\S]*?)\{\{\/if\}\}/g,
                  (_match: string, content: string) => isLast ? content : ''
                );
                
                // Handle {{#unless @first}}...{{/unless}} - NOVO
                itemContent = itemContent.replace(
                  /\{\{#unless\s+@first\}\}([\s\S]*?)\{\{\/unless\}\}/g,
                  (_match: string, content: string) => isFirst ? '' : content
                );
                
                // Handle {{#unless @last}}...{{/unless}} - SUA CORREÇÃO IMPLEMENTADA
                itemContent = itemContent.replace(
                  /\{\{#unless\s+@last\}\}([\s\S]*?)\{\{\/unless\}\}/g,
                  (_match: string, content: string) => isLast ? '' : content
                );
                
                // Handle object properties like {{property_name}} with enhanced type handling
                if (typeof item === 'object' && item !== null) {
                  Object.keys(item).forEach(key => {
                    const value = item[key];
                    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
                    
                    // Better type handling for object properties
                    let stringValue = '';
                    if (value === null || value === undefined) {
                      stringValue = '';
                    } else if (typeof value === 'object') {
                      stringValue = Array.isArray(value) ? value.join(', ') : JSON.stringify(value);
                    } else {
                      stringValue = String(value);
                    }
                    
                    itemContent = itemContent.replace(regex, stringValue);
                  });
                }
                
                return itemContent;
              })
              .join('');
          }
          return match; // Keep original if array not found
        }
      );

      // Handle simple {{variable}} and {{object.property}} substitution DEPOIS dos loops
      processedTemplate = processedTemplate.replace(/\{\{([\w.]+)\}\}/g, (match, variablePath) => {
        try {
          // Handle nested object paths like demographics.gender
          const value = this.getNestedValue(variables, variablePath);
          if (value !== undefined && value !== null) {
            // Handle objects and arrays by converting to string representation
            if (typeof value === 'object') {
              if (Array.isArray(value)) {
                // For arrays, create a readable list with better formatting
                return value.map((item, idx) => {
                  if (typeof item === 'object' && item !== null) {
                    // For object arrays, show key properties
                    const name = item.name_localized || item.name || item.cause_name || item.symptom_name || item.property_name || `Item ${idx + 1}`;
                    const explanation = item.explanation_localized || item.explanation || item.description || '';
                    return explanation ? `${name}: ${explanation}` : name;
                  }
                  return String(item);
                }).join('\n- ');
              } else {
                // For objects, show key-value pairs with better formatting
                return Object.entries(value)
                  .filter(([_, val]) => val !== null && val !== undefined)
                  .map(([key, val]) => `${key}: ${val}`)
                  .join(', ');
              }
            }
            return String(value);
          }
          // Return empty string for null/undefined instead of original placeholder
          return '';
        } catch (error) {
          console.warn(`Template variable substitution failed for ${variablePath}:`, error);
          return match; // Keep original if error occurred
        }
      });

      // Handle any remaining conditional blocks FORA de loops com warning
      processedTemplate = processedTemplate.replace(
        /\{\{#(if|unless)\s+@(first|last)\}\}([\s\S]*?)\{\{\/\1\}\}/g,
        (_match: string, condition: string, variable: string, _content: string) => {
          console.warn(`{{#${condition} @${variable}}} found outside of {{#each}} loop context - removing`);
          return ''; // Remove orphaned conditionals
        }
      );

      return processedTemplate;
    } catch (error) {
      throw new PromptManagerError(
        'Failed to process template variables',
        'TEMPLATE_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * Get processed prompt with variables substituted
   * Returns both system instructions and user message if defined
   */
  public async getProcessedPrompt(
    promptName: string, 
    variables: TemplateVariables
  ): Promise<{ 
    prompt: string; 
    systemInstructions: string;
    userMessage: string;
    config: PromptConfig 
  }> {
    try {
      const promptConfig = await this.loadPromptConfig(promptName);
      const processedSystemPrompt = this.processTemplate(promptConfig.template, variables);
      
      // Process user_message if it exists in the YAML, otherwise create a default
      let processedUserMessage = '';
      if (promptConfig['user_message'] && typeof promptConfig['user_message'] === 'string') {
        processedUserMessage = this.processTemplate(promptConfig['user_message'], variables);
      } else {
        // Create default user message from key template variables
        const userMessageParts: string[] = [];
        
        if (variables['health_concern']) {
          userMessageParts.push(`Health concern: ${variables['health_concern']}`);
        }
        
        if (variables['demographics'] && typeof variables['demographics'] === 'object') {
          const demo = variables['demographics'];
          const demoParts: string[] = [];
          if (demo.gender) demoParts.push(`Gender: ${demo.gender}`);
          if (demo.age_category) demoParts.push(`Age: ${demo.age_category}`);
          if (demoParts.length > 0) {
            userMessageParts.push(`Demographics: ${demoParts.join(', ')}`);
          }
        }
        
        processedUserMessage = userMessageParts.length > 0 
          ? userMessageParts.join('\n')
          : 'Please provide your analysis based on the provided context.';
      }

      return {
        prompt: processedSystemPrompt, // Backward compatibility
        systemInstructions: processedSystemPrompt,
        userMessage: processedUserMessage,
        config: promptConfig
      };
    } catch (error) {
      if (error instanceof PromptManagerError) {
        throw error;
      }

      throw new PromptManagerError(
        `Failed to get processed prompt: ${promptName}`,
        'PROCESS_ERROR',
        promptName,
        error as Error
      );
    }
  }

  /**
   * Clear the prompt cache
   */
  public clearCache(): void {
    this.promptCache.clear();
  }

  /**
   * Get cached prompt names
   */
  public getCachedPrompts(): string[] {
    return Array.from(this.promptCache.keys());
  }

  /**
   * Validate prompt configuration structure
   */
  private validatePromptConfig(config: any, promptName: string): void {
    const requiredFields = ['version', 'description', 'config', 'template', 'schema'];
    
    for (const field of requiredFields) {
      if (!config.hasOwnProperty(field)) {
        throw new PromptManagerError(
          `Missing required field '${field}' in prompt configuration`,
          'VALIDATION_ERROR',
          promptName
        );
      }
    }

    // Validate config section
    if (!config.config || typeof config.config !== 'object') {
      throw new PromptManagerError(
        'Invalid config section in prompt configuration',
        'VALIDATION_ERROR',
        promptName
      );
    }

    const requiredConfigFields = ['model', 'temperature', 'max_tokens'];
    for (const field of requiredConfigFields) {
      if (!config.config.hasOwnProperty(field)) {
        throw new PromptManagerError(
          `Missing required config field '${field}' in prompt configuration`,
          'VALIDATION_ERROR',
          promptName
        );
      }
    }

    // Validate template
    if (typeof config.template !== 'string' || config.template.trim().length === 0) {
      throw new PromptManagerError(
        'Template must be a non-empty string',
        'VALIDATION_ERROR',
        promptName
      );
    }

    // Validate schema
    if (!config.schema || typeof config.schema !== 'object') {
      throw new PromptManagerError(
        'Schema must be a valid object',
        'VALIDATION_ERROR',
        promptName
      );
    }
  }

  /**
   * Preload prompt configurations for all available steps
   */
  public async preloadPrompts(): Promise<void> {
    try {
      const availablePrompts = await this.getAvailablePrompts();
      await Promise.all(
        availablePrompts.map(name => this.loadPromptConfig(name))
      );
    } catch (error) {
      throw new PromptManagerError(
        'Failed to preload prompt configurations',
        'PRELOAD_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * Check if a prompt configuration exists
   */
  public async promptExists(promptName: string): Promise<boolean> {
    try {
      const filePath = path.join(this.promptsBasePath, `${promptName}.yaml`);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available prompt names by scanning the prompts directory
   */
  public async getAvailablePrompts(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.promptsBasePath);
      return files
        .filter(file => file.endsWith('.yaml'))
        .map(file => file.replace('.yaml', ''));
    } catch (error) {
      throw new PromptManagerError(
        'Failed to scan prompts directory',
        'SCAN_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * Dynamically preload prompts based on available files
   */
  public async preloadAvailablePrompts(): Promise<void> {
    try {
      const availablePrompts = await this.getAvailablePrompts();
      await Promise.all(
        availablePrompts.map(name => this.loadPromptConfig(name))
      );
    } catch (error) {
      throw new PromptManagerError(
        'Failed to preload available prompt configurations',
        'PRELOAD_AVAILABLE_ERROR',
        undefined,
        error as Error
      );
    }
  }
}

/**
 * Convenience function to get the singleton instance
 */
export function getPromptManager(): PromptManager {
  return PromptManager.getInstance();
}

/**
 * Convenience function to load and process a prompt
 */
export async function loadAndProcessPrompt(
  promptName: string,
  variables: TemplateVariables
): Promise<{ 
  prompt: string; 
  systemInstructions: string;
  userMessage: string;
  config: PromptConfig 
}> {
  const manager = getPromptManager();
  return manager.getProcessedPrompt(promptName, variables);
}
