but # Docker Deployment Plan for Next.js Application

This document outlines the steps required to containerize the Next.js application using Docker for production deployment.

## Prerequisites

1.  **Docker Desktop:** Ensure you have Docker Desktop installed and running on your local machine. You can download it from the [official Docker website](https://www.docker.com/products/docker-desktop/).

## Step-by-Step Guide

### Step 1: Configure Next.js for Standalone Output

To create an optimized Docker image, we should configure Next.js to produce a standalone build. This will copy only the necessary files and dependencies into a separate folder, drastically reducing the image size.

**Action:**
Modify `next.config.ts` to include the `output: 'standalone'` option.

```typescript
// next.config.ts

/** @type {import('next').NextConfig} */
const nextConfig = {
  // ... other configurations
  output: 'standalone',
};

export default nextConfig;
```

### Step 2: Create a `Dockerfile`

The `Dockerfile` contains the instructions to build our application image. We will use a multi-stage build to keep the final image lean.

**Action:**
Create a new file named `Dockerfile` in the root of the project with the following content:

```dockerfile
# Stage 1: Install dependencies
FROM node:18-alpine AS deps
WORKDIR /app

# Copy package.json and lockfile
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Stage 2: Build the application
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the Next.js application
RUN npm run build

# Stage 3: Production Image
FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production

# Copy the standalone output from the builder stage
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Expose the port the app runs on
EXPOSE 3000

# Set the user to run the app
USER nextjs

# Start the Next.js server
CMD ["node", "server.js"]
```

### Step 3: Create a `.dockerignore` File

To keep the build context small and avoid copying unnecessary files into the image, we'll create a `.dockerignore` file.

**Action:**
Create a new file named `.dockerignore` in the root of the project. Its content should be similar to `.gitignore`.

```
# See .gitignore for comments

.env
.env*.local
.git
.gitignore
.dockerignore
.next
.vscode
node_modules
npm-debug.log*
yarn-error.log*
README.md
```

### Step 4: Build the Docker Image

Now, we can build the image using the `docker build` command.

**Action:**
Open your terminal in the project root and run:

```bash
docker build -t bedrock-v3-app .
```

-   `-t bedrock-v3-app` tags the image with the name `bedrock-v3-app`.

### Step 5: Run the Docker Container

Once the image is built, you can run it as a container.

**Action:**
Run the following command in your terminal:

```bash
docker run -p 3000:3000 bedrock-v3-app
```

-   `-p 3000:3000` maps port 3000 from the container to port 3000 on your local machine.

Your application should now be accessible at `http://localhost:3000`.

### (Optional) Using Docker Compose for Easier Management

For a more streamlined development and deployment process, you can use Docker Compose.

**Action:**
Create a `docker-compose.yml` file in the project root:

```yaml
version: '3.8'
services:
  web:
    build: .
    container_name: bedrock_v3_container
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      # Add any other necessary environment variables here
      # - DATABASE_URL=...
      # - NEXT_PUBLIC_API_URL=...
```

You can then build and run your application with a single command:

```bash
# Build and start the container in detached mode
docker-compose up -d --build

# Stop the container
docker-compose down
```

## Next Steps

After building and testing the image locally, you can push it to a container registry like Docker Hub, Amazon ECR, or Google Container Registry for deployment to a production environment.
