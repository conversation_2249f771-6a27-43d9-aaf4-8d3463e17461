
import { Suspense } from 'react';
import { ResetPasswordForm } from '@/features/auth/components';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

/**
 * Loading fallback component for the reset password form
 */
function ResetPasswordLoading() {
  return (
    <div className="w-full animate-fade-in">
      <Card className="w-full shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold">Reset Password</CardTitle>
          <CardDescription>Loading form...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Renders the "Reset Password" page for the PassForge application.
 * Users arrive at this page after clicking a password reset link in their email.
 * It allows them to set a new password.
 * It primarily displays the `ResetPasswordForm` component which handles the form submission and password update logic.
 * Uses Suspense boundary to handle dynamic useSearchParams() usage in ResetPasswordForm.
 *
 * @returns {JSX.Element} The reset password page component.
 */
export default function ResetPasswordPage(): JSX.Element {
  return (
    <Suspense fallback={<ResetPasswordLoading />}>
      <ResetPasswordForm />
    </Suspense>
  );
}
