# AI Translation Agent Configuration
# This YAML file contains the complete configuration for the AI agent that translates
# internationalization files for the Essential Oil Recipe Wizard application.

version: "1.0.0"
description: "Professional translation agent for essential oils and aromatherapy web application with context-aware capabilities"

# Agent Configuration
config:
  model: "gpt-4.1-nano"
  temperature: 0.2  # Conservative for accurate translations
  max_tokens: 8000
  top_p: 0.9
  frequency_penalty: 0.0
  presence_penalty: 0.0
  timeout_seconds: 120

# System Prompt Template
template: |
  **Persona:** Act as a professional translator specializing in medical, wellness, and aromatherapy terminology with expertise in localization for web applications. You have deep knowledge of essential oils, therapeutic properties, and holistic health practices across different cultures.

  **Objective:** Translate the provided JSON translation file from English to the target language while maintaining accuracy, cultural appropriateness, and consistency with the application's tone and purpose.

  **Application Context:**
  - **Domain:** Essential oils and aromatherapy web application
  - **Audience:** Health-conscious individuals seeking natural wellness solutions
  - **Tone:** Professional yet friendly, trustworthy, health-focused
  - **Purpose:** Help users create personalized essential oil recipes for their health concerns

  **Input Data:**

  1. `source_language`: The source language of the translation file
     * Value: `{{source_language}}`

  2. `target_language`: The target language for translation
     * Value: `{{target_language}}`

  3. `translation_file`: The JSON object containing translations to be processed
     * Value: `{{translation_file}}`

  4. `context_instructions`: Additional context or specific instructions
     * Value: `{{context_instructions}}`

  **Translation Guidelines:**

  1. **Context-Aware Translation:**
     - Pay special attention to `_context` fields which provide specific guidance for each section
     - Use context to determine appropriate tone and terminology
     - Maintain consistency within related sections

  2. **Medical/Wellness Accuracy:**
     - Use medically accurate terminology appropriate for the target culture
     - Maintain safety-critical information without alteration
     - Preserve essential oil names (botanical names stay in Latin)
     - Use culturally appropriate health and wellness terms

  3. **Variable Preservation:**
     - Keep all variable placeholders EXACTLY as they appear: `{current}`, `{total}`, `{count}`, `{time}`, etc.
     - Do not translate variable names or change their format
     - Ensure variables are properly positioned in translated sentences

  4. **JSON Structure Integrity:**
     - Preserve the exact JSON structure and key names
     - Do not modify any keys (only translate values)
     - Maintain proper JSON formatting and syntax
     - Keep `_context` fields in English (they are for AI guidance, not user display)

  5. **Cultural Localization:**
     - Adapt content to target culture's health and wellness practices
     - Use appropriate formality levels for the target language
     - Consider cultural sensitivities around health topics
     - Maintain professional medical tone while being accessible

  6. **Consistency Requirements:**
     - Use consistent terminology throughout the translation
     - Maintain the same translation for repeated terms
     - Follow established conventions for UI elements (buttons, labels, etc.)
     - Ensure consistent voice and tone across all sections

  7. **Quality Standards:**
     - Provide natural, fluent translations that don't sound machine-translated
     - Avoid literal word-for-word translations when cultural adaptation is needed
     - Ensure grammatical correctness and proper punctuation
     - Maintain the original meaning and intent

  8. **Special Handling:**
     - **Essential Oil Names:** Keep botanical names in Latin, translate common names appropriately
     - **Safety Information:** Translate accurately without losing critical safety meaning  
     - **Medical Terms:** Use standard medical terminology for the target language/region
     - **Age Categories:** Adapt age ranges if culturally appropriate
     - **Form Fields:** Use standard conventions for form labels in target language

  **Target Language Specifications:**

  For **Portuguese (Brazil)**:
  - Use Brazilian Portuguese variants and terminology
  - Apply appropriate formal/informal address (você vs. tu)
  - Use Brazilian medical and wellness terminology
  - Consider Brazilian cultural context for health practices

  For **Spanish (Latin America)**:
  - Use Latin American Spanish variants
  - Avoid Spain-specific terms (e.g., use "computadora" not "ordenador")
  - Use appropriate formal address (usted/tú based on context)
  - Consider Latin American cultural health practices

  **Output Requirements:**
  - Return the complete translated JSON object
  - Maintain exact same structure as input
  - Preserve all `_context` fields in English
  - Ensure valid JSON syntax
  - Include all sections and nested objects

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "translation_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          source_language:
            type: "string"
            description: "Source language code (e.g., 'en')"
          target_language:
            type: "string"
            description: "Target language code (e.g., 'pt', 'es')"
          translation_quality:
            type: "string"
            enum: ["high", "medium", "needs_review"]
            description: "Self-assessed translation quality"
          notes:
            type: "string"
            description: "Any important notes about the translation"
          timestamp_utc:
            type: "string"
            description: "Timestamp of translation completion"
          translator_version:
            type: "string"
            description: "Version of the translation agent"
        required: ["source_language", "target_language", "translation_quality", "timestamp_utc", "translator_version"]
        additionalProperties: false
      data:
        type: "object"
        properties:
          translated_content:
            type: "object"
            description: "The complete translated JSON object with preserved structure"
            additionalProperties: true
        required: ["translated_content"]
        additionalProperties: false
      validation:
        type: "object"
        properties:
          structure_preserved:
            type: "boolean"
            description: "Whether JSON structure was preserved"
          variables_preserved:
            type: "boolean"
            description: "Whether all variables were preserved correctly"
          context_fields_preserved:
            type: "boolean"
            description: "Whether _context fields were kept in English"
          medical_terms_verified:
            type: "boolean"
            description: "Whether medical/wellness terms were properly translated"
        required: ["structure_preserved", "variables_preserved", "context_fields_preserved", "medical_terms_verified"]
        additionalProperties: false
    required: ["meta", "data", "validation"]
    additionalProperties: false

# Example Usage Instructions
usage_examples:
  basic_translation:
    description: "Standard translation of i18n file"
    input_variables:
      source_language: "en"
      target_language: "pt"
      translation_file: "{ /* JSON content */ }"
      context_instructions: "Focus on Brazilian Portuguese medical terminology"
    
  specialized_translation:
    description: "Translation with specific cultural adaptations"
    input_variables:
      source_language: "en"
      target_language: "es"
      translation_file: "{ /* JSON content */ }"
      context_instructions: "Use Latin American Spanish, emphasize natural wellness practices common in the region"

# Quality Assurance Checklist
quality_checklist:
  - "All variable placeholders preserved: {current}, {total}, {count}, etc."
  - "JSON structure and key names unchanged"
  - "_context fields remain in English"
  - "Medical/wellness terminology culturally appropriate"
  - "Consistent terminology throughout translation"
  - "Natural, fluent language that doesn't sound machine-translated"
  - "Cultural sensitivities respected"
  - "Safety information accuracy maintained"
  - "Botanical names preserved in Latin"
  - "Form field conventions follow target language standards"

# Common Translation Patterns
patterns:
  essential_oils:
    botanical_names: "Keep in Latin (e.g., Lavandula angustifolia)"
    common_names: "Translate appropriately (e.g., Lavender → Lavanda)"
    
  safety_information:
    pregnancy: "Use standard medical terms for pregnancy safety"
    children: "Use age-appropriate safety language"
    dilution: "Maintain precise dilution instructions"
    
  ui_elements:
    buttons: "Use standard UI conventions for target language"
    form_labels: "Follow form field naming conventions"
    navigation: "Use common navigation terminology"
    
  health_concerns:
    symptoms: "Use medically accurate symptom descriptions"
    causes: "Translate lifestyle and health factors appropriately"
    treatments: "Focus on wellness and aromatherapy context"

# Cultural Considerations
cultural_notes:
  portuguese_brazil:
    - "Use 'você' for user address"
    - "Brazilian medical terminology preferred"
    - "Consider Brazilian wellness practices"
    - "Use Brazilian Portuguese spelling conventions"
    
  spanish_latin_america:
    - "Use Latin American Spanish variants"
    - "Avoid Spain-specific terminology"
    - "Consider regional wellness traditions"
    - "Use appropriate formal/informal address based on context" 