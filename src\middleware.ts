import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/middleware';
import * as Sentry from '@sentry/nextjs';

// Define public paths that don't require authentication
const PUBLIC_PATHS = ['/login', '/register', '/reset-password', '/'];

// Add development-only public paths for testing
const DEVELOPMENT_PUBLIC_PATHS = process.env.NODE_ENV === 'development'
  ? ['/api/ai/streaming']
  : [];

const ALL_PUBLIC_PATHS = [...PUBLIC_PATHS, ...DEVELOPMENT_PUBLIC_PATHS];

// Cache session data for a short period to reduce redundant calls during streaming
const sessionCache = new Map<string, { session: any; timestamp: number; error?: any }>();
const CACHE_DURATION = 10000; // 5 seconds cache for session data

function getCacheKey(request: NextRequest): string {
  // Create cache key based on auth headers to identify the same session
  const authHeader = request.headers.get('authorization') || '';
  const cookieHeader = request.headers.get('cookie') || '';
  return `${authHeader}-${cookieHeader}`.slice(0, 100); // Limit key length
}

function getCachedSession(cacheKey: string) {
  const cached = sessionCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached;
  }
  return null;
}

function setCachedSession(cacheKey: string, session: any, error?: any) {
  sessionCache.set(cacheKey, {
    session,
    timestamp: Date.now(),
    error
  });
  
  // Clean up old cache entries
  if (sessionCache.size > 100) {
    const now = Date.now();
    const keysToDelete: string[] = [];
    sessionCache.forEach((value, key) => {
      if (now - value.timestamp > CACHE_DURATION) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => sessionCache.delete(key));
  }
}

export async function middleware(request: NextRequest) {
  try {
    // Check if path is public first to avoid unnecessary auth checks
    const isPublicPath = ALL_PUBLIC_PATHS.some(path =>
      request.nextUrl.pathname === path ||
      request.nextUrl.pathname.startsWith(`${path}/`)
    );
    
    // Skip auth entirely for public paths
    if (isPublicPath) {
      return NextResponse.next();
    }

    // Check cache for recent session data
    const cacheKey = getCacheKey(request);
    const cachedResult = getCachedSession(cacheKey);
    
    let session, sessionError;
    
    if (cachedResult) {
      session = cachedResult.session;
      sessionError = cachedResult.error;
      
      // Add debug header to track cache hits
      const response = NextResponse.next();
      if (process.env.NODE_ENV === 'development') {
        response.headers.set('x-session-cache', 'hit');
      }
    } else {
      // Create supabase middleware client only when needed
      const { supabase, response } = createClient(request);
      
      // Get session with minimal logging
      const result = await supabase.auth.getSession();
      session = result.data?.session;
      sessionError = result.error;
      
      // Cache the result
      setCachedSession(cacheKey, session, sessionError);
      
      // Add debug header to track cache misses
      if (process.env.NODE_ENV === 'development') {
        response.headers.set('x-session-cache', 'miss');
      }
    }
    
    // Handle session errors
    if (sessionError) {
      // Expected auth errors (not logged to console or Sentry)
      if (sessionError.message === "Auth session missing!") {
        // Just redirect without logging
        const redirectUrl = new URL('/login', request.url);
        redirectUrl.searchParams.set('next', request.nextUrl.pathname);
        return NextResponse.redirect(redirectUrl);
      }
      // Unexpected auth errors (logged to console and Sentry)
      else {
        console.warn(`[Middleware] Auth error: ${sessionError.message}`);
        
        // Report to Sentry with proper context
        Sentry.captureException(sessionError, {
          tags: { component: 'Middleware', type: 'sessionError' },
          extra: { 
            path: request.nextUrl.pathname,
            operation: 'middleware',
            message: "Session error in middleware"
          }
        });
        
        // Redirect to login
        const redirectUrl = new URL('/login', request.url);
        redirectUrl.searchParams.set('next', request.nextUrl.pathname);
        return NextResponse.redirect(redirectUrl);
      }
    }
    
    // Handle protected routes
    if (!session) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('next', request.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }
    
    // Handle auth routes when already authenticated
    if (session && (
      request.nextUrl.pathname === '/login' || 
      request.nextUrl.pathname === '/register'
    )) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    
    // Continue with the response
    return NextResponse.next();
  } catch (err) {
    // Simple error handling for Edge Runtime (no Winston logger available)
    if (process.env.NODE_ENV === 'development') {
      console.error('[Middleware] Critical error:', err);
    }

    // Report to Sentry
    Sentry.captureException(err, {
      tags: { component: 'Middleware', type: 'criticalError' },
      extra: {
        path: request.nextUrl.pathname,
        operation: 'middleware',
        message: "Critical error in middleware"
      }
    });

    // For critical errors, allow the request to proceed
    // The application's error boundaries will handle rendering
    return NextResponse.next();
  }
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
