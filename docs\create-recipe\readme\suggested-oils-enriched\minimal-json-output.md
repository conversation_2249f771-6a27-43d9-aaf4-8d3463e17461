# Minimal JSON Output for Essential Oil Recommendation Pipeline

## Overview

This document describes the structure, enrichment process, and rationale behind the **minimal JSON output** generated during the `create-recipe` flow. This output is the foundation for all subsequent steps in the essential oil recommendation and protocol-building pipeline.

---

## 1. Purpose

The minimal JSON output:
- **Normalizes** all data (no duplication, single source of truth for oils)
- **Enriches** the data with calculated scores for transparency and ranking
- **Prepares** the data for final aggregation, ranking, and protocol generation
- **Serves as the canonical interface** for downstream features (protocol builder, UI, export, etc.)

---

## 2. Structure

The minimal JSON output contains the following top-level fields:

- `user_language`: The user's selected language (e.g., `PT_BR`)
- `healthConcern`: The user's health concern input
- `demographics`: User demographic data (gender, age, etc.)
- `selectedCauses`: Array of selected causes (with IDs, names, explanations)
- `selectedSymptoms`: Array of selected symptoms (with IDs, names, explanations)
- `oils`: **Central, deduplicated array** of all unique essential oils (static data only)
- `therapeuticProperties`: Array of properties, each with:
  - Property details (ID, name, description, relevancy)
  - `propertyCoverageScore`: Calculated score for property relevance
  - `suggested_oils`: Array of oil references for this property, each with:
    - `oil_id`: Reference to the oil in the top-level `oils` array
    - `match_rationale_localized`: Why this oil is suggested for this property
    - `relevancy_to_property_score`: Oil's score for this property
    - `recommendationInstanceScore`: Calculated score for this oil-property pair
- `finalRankedOils`: Array of oils, sorted by their total relevance score across all properties

---

## 3. Enrichment & Scoring Logic

### a. Property Coverage Score
- **Field:** `propertyCoverageScore`
- **Purpose:** Indicates how relevant a property is to the user's specific problem
- **Calculation:**
  - `propertyCoverageScore = relevancy_score / MAX_RELEVANCY_SCORE`
  - (Where `MAX_RELEVANCY_SCORE` is typically 5)

### b. Recommendation Instance Score
- **Field:** `recommendationInstanceScore`
- **Purpose:** Quantifies the strength of recommending a specific oil for a property
- **Calculation:**
  - `recommendationInstanceScore = relevancy_to_property_score × relevancy_score × propertyCoverageScore`

### c. Final Ranked Oils
- **Field:** `finalRankedOils`
- **Purpose:** Provides a simple, sorted list of the best oils for the user
- **Calculation:**
  - For each oil, sum all `recommendationInstanceScore` values across all properties
  - Sort descending by this total

---

## 4. Example Output

```json
{
  "user_language": "PT_BR",
  "healthConcern": { "healthConcern": "dor no joelho" },
  "demographics": { "gender": "female", "ageCategory": "teen", "specificAge": 17 },
  "selectedCauses": [ ... ],
  "selectedSymptoms": [ ... ],
  "oils": [
    {
      "oil_id": "a9a905d3-...",
      "name_localized": "Copaíba",
      "name_english": "Copaiba",
      "name_scientific": "Copaifera reticulate, officinalis, coriacea, and langsdorffii",
      "safety": { ... }
    },
    // ... other unique oils
  ],
  "therapeuticProperties": [
    {
      "property_id": "c2f1d8e4-...",
      "property_name_localized": "Anti-inflamatório",
      "relevancy_score": 5,
      "propertyCoverageScore": 1.0,
      "suggested_oils": [
        {
          "oil_id": "a9a905d3-...",
          "match_rationale_localized": "Óleo potente anti-inflamatório...",
          "relevancy_to_property_score": 5,
          "recommendationInstanceScore": 25.0
        }
        // ...
      ]
    }
    // ...
  ],
  "finalRankedOils": [
    {
      "oil_id": "a9a905d3-...",
      "name_localized": "Copaíba",
      "finalRelevanceScore": 40.00
    }
    // ...
  ]
}
```

---

## 5. Rationale & Best Practices

- **No duplication:** Oils are defined once in the `oils` array and referenced by `oil_id` elsewhere.
- **Static vs. dynamic data:** Static oil info stays in `oils`; dynamic, context-specific scores live in `therapeuticProperties` and `finalRankedOils`.
- **Transparency:** All scores are explicit, making the recommendation logic auditable and explainable.
- **Extensibility:** This structure supports future features (protocol builder, export, analytics) without breaking changes.

---

## 6. Usage in Future Steps

- **Protocol Generation:** The `finalRankedOils` array is the starting point for building user protocols.
- **UI Display:** The enriched `therapeuticProperties` allow for detailed, property-by-property explanations in the UI.
- **Export/Integration:** This minimal JSON can be safely exported, versioned, or sent to other services as a canonical record of the recommendation process.

---

## 7. Maintenance & Versioning

- **Always update this document** if the minimal JSON structure changes.
- **Keep enrichment logic in sync** with this documentation and the codebase.
- **Use this as the onboarding reference** for new developers working on the create-recipe pipeline.

---

## 8. References
- See `src/features/create-recipe/utils/recommendation-scoring.ts` for enrichment logic
- See `src/features/create-recipe/components/recipe-debug-overlay.tsx` for minimal JSON generation 