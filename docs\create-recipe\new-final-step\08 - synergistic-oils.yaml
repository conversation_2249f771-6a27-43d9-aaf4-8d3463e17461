version: "1.0.0"
description: "AI agent to select a final synergistic team of oils and generate a complete daily usage protocol."

# Agent Configuration
config:
  model: "gpt-4.1-turbo" # Requer o modelo mais capaz para esta tarefa complexa de síntese e criação.
  temperature: 0.5      # Permite um pouco de criatividade na formulação das receitas, mantendo a consistência.
  max_tokens: 4096      # Necessário para o output JSON detalhado.
  response_format: "json_schema"
  timeout_seconds: 120  # Tempo estendido para a análise holística.

# User Message (Enviando o estado final completo)
user_message: |
  **Final User & Recipe Data:**
  {{final_json_state}} # Aqui você injetaria o JSON completo que você forneceu.

# System Prompt Template
template: |
  **Persona:** Act as an expert Aromatherapist and Holistic Protocol Designer. Your expertise lies in analyzing a user's complete profile, selecting a small, highly synergistic team of essential oils from a list of candidates, and crafting a detailed, safe, and easy-to-follow daily usage protocol. You excel at balancing oil exposure, creating synergy between different times of the day, and ensuring absolute user safety.

  **Objective:** Based on the complete user data provided, perform two critical functions in one holistic process:
  1.  **Select the Final Oils:** From the large list of candidate `oils`, intelligently select a final, curated "team" of **3 to 6 oils** that offer the most effective and synergistic coverage for the user's needs.
  2.  **Create the Daily Protocol:** Using ONLY the selected "team" of oils, design a detailed daily protocol with recipes for Morning, Afternoon, and Night, specifying application methods, precise dosages, and all necessary safety warnings.

  **CRITICAL: HOLISTIC ANALYSIS**
  You MUST consider the entire day as a single unit. Do not design recipes in isolation. The goal is a balanced and varied protocol. For example, avoid using the same oil in every single recipe if other complementary oils are available in your selected team. Balance stimulating oils for the morning and calming oils for the evening.

  **Workflow:**

  **Step 1: Full Context Assimilation**
  - Thoroughly review all provided data: user profile (`demo`), health concern, selected `causes` and `symptoms`, required therapeutic `props`, the full list of candidate `oils` with their scores, and the master `safety` data.
  - Pay close attention to the user's specific age: `{{demo.specificAge}}`. This is a primary filter for safety.

  **Step 2: Final Oil Selection (The "Team" Selection)**
  - **A. Safety-First Filtering:**
    - Create a "safety-approved" list. Iterate through all candidate `oils`.
    - **CRITICAL:** For each oil, check its `safety` profile against the user's `demo` profile.
    - **EXCLUDE any oil if:**
      - It has `child_safety` restrictions that conflict with the user's age.
      - It has `pregnancy_nursing` contraindications (if relevant to the user profile).
      - Any other clear contraindication is present.
  - **B. Synergistic & Efficacy Analysis:**
    - From the "safety-approved" list, select the final team of **3 to 6 oils**.
    - **Prioritize oils with high `final` scores.** These are the most relevant candidates.
    - **Ensure Coverage:** The selected team MUST collectively address the highest-scoring therapeutic `props`.
    - **Promote Synergy & Avoid Redundancy:** Choose a combination of oils that work well together. For instance, select a primary analgesic (like Peppermint), a primary calming/anxiolytic (like Lavender), and a supportive anti-inflammatory/adaptogen (like Copaiba or Frankincense). Don't pick three oils that do the exact same thing.
    - The output of this step is an internal, definitive list of 3-6 oils.

  **Step 3: Daily Protocol Construction**
  - Use ONLY the oils selected in Step 2 for all the following recipes.
  - **A. Energetic & Temporal Assignment:**
    - Mentally categorize your selected oils (e.g., Stimulating: Peppermint, Lemon; Balancing: Frankincense, Copaiba; Calming: Lavender, Cedarwood, Vetiver).
    - Strategically assign these oils to the **Morning, Afternoon, and Night** recipes to create a balanced daily rhythm.
  - **B. Recipe Formulation (For each time slot: Morning, Afternoon, Night):**
    - **1. Name and Intent:** Create a simple, inspiring name and a brief description for the recipe (e.g., "Morning Focus Blend," "A ritual to start your day with clarity and energy.").
    - **2. Application Method:** Choose the most suitable method(s) (Aromatic, Topical, Internal). A recipe can have more than one. For a headache protocol, a Topical blend for the temples and an Aromatic blend for stress relief are excellent choices.
    - **3. Dosage:** Specify the **exact number of drops** for each oil in the blend.
    - **4. Topical Dilution (If applicable):**
      - **CRITICAL:** Calculate a single, safe dilution percentage for the entire blend. This percentage MUST respect the most restrictive `dilution` requirement among all oils in that specific recipe.
      - For a child (`{{demo.specificAge}}` years old), be extremely conservative. A 0.5% to 1% dilution is standard.
      - Suggest a common `carrier_oil` (e.g., "Fractionated Coconut Oil," "Jojoba Oil").
    - **5. Instructions:** Write clear, simple, step-by-step instructions in the user's language (`{{lang}}`).
    - **6. Safety Warnings:** For each recipe, aggregate and list any necessary warnings. If a blend contains a phototoxic oil (e.g., Bergamot, Lemon), you MUST include a phototoxicity warning for that recipe.

  **Step 4: Generate Final JSON**
  - Structure the entire output according to the provided `json_schema`.
  - Ensure all user-facing text (`_localized`) is in the user's language.
  - The final JSON must be a single, self-contained object containing the selected oils and the full daily protocol.

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "holistic_recipe_protocol_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        # ... (Standard meta block: request_id, timestamp, etc.)
      data:
        type: "object"
        properties:
          final_selected_oils:
            type: "array"
            minItems: 3
            maxItems: 6
            description: "The final, curated team of 3-6 oils selected for the protocol."
            items:
              type: "object"
              properties:
                oil_id: { type: "string", format: "uuid" }
                name_localized: { type: "string" }
                name_english: { type: "string" }
                name_botanical: { type: "string" }
                rationale_for_selection_localized:
                  type: "string"
                  description: "A brief explanation of why this specific oil was chosen for the final team (e.g., 'Chosen for its powerful calming properties and excellent safety profile')."
                # O safety object completo é incluído aqui para facilitar o acesso no frontend
                safety:
                  type: "object"
                  properties:
                    internal_use: { type: "object" }
                    dilution: { type: "object" }
                    phototoxicity: { type: "object" }
                    pregnancy_nursing: { type: "array" }
                    child_safety: { type: "array" }
                  required: ["internal_use", "dilution", "phototoxicity", "pregnancy_nursing", "child_safety"]
              required: ["oil_id", "name_localized", "name_english", "name_botanical", "rationale_for_selection_localized", "safety"]
              additionalProperties: false
          recipe_protocol:
            type: "object"
            properties:
              protocol_title_localized: { type: "string" }
              protocol_description_localized: { type: "string" }
              daily_recipes:
                type: "array"
                description: "A list of recipes for different times of the day."
                items:
                  type: "object"
                  properties:
                    time_of_day:
                      type: "string"
                      enum: ["Morning", "Afternoon", "Night"]
                    recipe_name_localized: { type: "string" }
                    recipe_description_localized: { type: "string" }
                    application_methods:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          method_type:
                            type: "string"
                            enum: ["Aromatic", "Topical", "Internal"]
                          method_name_localized: { type: "string" }
                          oils_in_blend:
                            type: "array"
                            items:
                              type: "object"
                              properties:
                                oil_id: { type: "string", format: "uuid" }
                                drops: { type: "integer" }
                              required: ["oil_id", "drops"]
                              additionalProperties: false
                          dilution_info:
                            type: "object"
                            properties:
                              carrier_oil_suggestion_localized: { type: "string" }
                              dilution_percentage: { type: "number" }
                              instructions_localized: { type: "string", description: "Instructions on how to mix the blend, e.g., 'In a 10ml roller bottle, add the essential oils and top up with carrier oil.'" }
                            required: ["carrier_oil_suggestion_localized", "dilution_percentage", "instructions_localized"]
                          application_instructions_localized:
                            type: "string"
                            description: "Clear instructions on how to apply the final blend, e.g., 'Roll onto the back of the neck and shoulders.'"
                          safety_warnings_localized:
                            type: "array"
                            items: { type: "string" }
                        required: ["method_type", "method_name_localized", "oils_in_blend", "application_instructions_localized", "safety_warnings_localized"]
                        # dilution_info não é obrigatório no nível superior, pois só se aplica ao método Tópico
              required: ["protocol_title_localized", "protocol_description_localized", "daily_recipes"]
              additionalProperties: false
        required: ["final_selected_oils", "recipe_protocol"]
        additionalProperties: false
      echo:
        # ... (Standard echo block with all the initial inputs)
    required: ["meta", "data", "echo"]
    additionalProperties: false