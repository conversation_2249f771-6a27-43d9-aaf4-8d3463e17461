import { useCallback } from 'react';
import { useParallelStreamingEngine, ParallelStreamRequest } from '@/lib/ai/hooks/use-parallel-streaming-engine';
import { createStreamRequest } from '../utils/api-data-transform';
import type {
  TherapeuticProperty, 
  PropertyOilSuggestions,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom
} from '../types/recipe.types';
import { DEFAULT_API_LANGUAGE } from '../constants/recipe.constants';

export function useCreateRecipeStreaming() {
  const { streamingState, startStreams, resetState } = useParallelStreamingEngine<any>();

  const startOilSuggestionStreaming = useCallback(async (
    properties: TherapeuticProperty[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    const requests: ParallelStreamRequest[] = properties.map(property => {
      const requestData = createStreamRequest(
        'create-recipe',
        'suggested-oils',
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        property
      );
      
      // Creating oil suggestion request for property
      
      return {
        id: property.property_id,
        url: '/api/ai/streaming',
        requestData,
        label: property.property_name,
        responseParser: (updates) => {
          // Updated for new schema: suggested_oils is now directly in data
          if (updates.finalData?.data?.suggested_oils) {
            return {
              therapeutic_property_context: updates.finalData.data.therapeutic_property_context,
              suggested_oils: updates.finalData.data.suggested_oils
            };
          }
          return null;
        }
      };
    });

    return await startStreams(requests);
  }, [startStreams]);

  const startOilEnrichmentStreaming = useCallback(async (
    propertySuggestions: PropertyOilSuggestions[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    const propertiesToEnrich = propertySuggestions.filter(
      suggestion => suggestion.suggested_oils && suggestion.suggested_oils.length > 0
    );

    console.log('🔍 [Oil Enrichment] Starting oil enrichment for properties:', propertiesToEnrich.length);

    const requests: ParallelStreamRequest[] = propertiesToEnrich.map(suggestion => ({
      id: suggestion.property_id,
      url: '/api/ai/streaming',
      requestData: createStreamRequest(
        'create-recipe',
        'oil-enrichment',
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        {
          property_id: suggestion.property_id,
          property_name: suggestion.property_name,
          property_name_english: suggestion.property_name_in_english || suggestion.property_name,
          description: suggestion.description,
          suggested_oils: suggestion.suggested_oils || []
        } as TherapeuticProperty
      ),
      label: suggestion.property_name,
      responseParser: (updates) => {
        console.log('🔍 [Oil Enrichment] Response parser received updates for:', suggestion.property_name, updates);
        console.log('🔍 [Oil Enrichment] Full updates structure:', JSON.stringify(updates, null, 2));
        
        if (updates.finalData?.data?.enriched_oils) {
          console.log('✅ [Oil Enrichment] Successfully parsed enriched oils:', updates.finalData.data.enriched_oils.length, 'oils');
          
          // Validate therapeutic property context is present
          if (updates.finalData.data.therapeutic_property_context) {
            console.log('✅ [Oil Enrichment] Therapeutic property context found:', updates.finalData.data.therapeutic_property_context.property_id);
          } else {
            console.warn('⚠️ [Oil Enrichment] Missing therapeutic property context in response');
          }
          
          return updates.finalData.data.enriched_oils;
        }
        
        console.log('❌ [Oil Enrichment] No enriched oils found in response structure');
        console.log('❌ [Oil Enrichment] finalData keys:', Object.keys(updates.finalData || {}));
        if (updates.finalData?.data) {
          console.log('❌ [Oil Enrichment] data keys:', Object.keys(updates.finalData.data || {}));
        }
        return null;
      }
    }));

    const results = await startStreams(requests);
    console.log('✅ [Oil Enrichment] Final enrichment results (Map):', results);
    console.log('✅ [Oil Enrichment] Results size:', results.size);
    console.log('✅ [Oil Enrichment] Results keys:', Array.from(results.keys()));
    console.log('✅ [Oil Enrichment] Results values preview:', Array.from(results.values()).map(v => Array.isArray(v) ? `Array(${v.length})` : typeof v));
    
    return results;
  }, [startStreams]);

  return {
    streamingState,
    startOilSuggestionStreaming,
    startOilEnrichmentStreaming,
    resetState,
  };
}
