
"use client";

import React, {
    useState,
    useEffect,
    useMemo,
    useCallback,
    forwardRef,
    useImperativeHandle,
    useLayoutEffect,
    useRef,
    type ComponentPropsWithoutRef,
} from 'react';
import { motion, AnimatePresence, type Transition, type Target, type VariantLabels, type AnimationControls, type TargetAndTransition } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface RotatingTextRef {
  next: () => void;
  previous: () => void;
  jumpTo: (index: number) => void;
  reset: () => void;
}

export interface RotatingTextProps
  extends Omit<
    ComponentPropsWithoutRef<typeof motion.span>,
    "children" | "transition" | "initial" | "animate" | "exit"
  > {
  texts: string[];
  transition?: Transition;
  initial?: Target | VariantLabels; // Allow Target or VariantLabels
  animate?: boolean | VariantLabels | AnimationControls | TargetAndTransition;
  exit?: Target | VariantLabels; // Allow Target or VariantLabels
  animatePresenceMode?: "sync" | "wait";
  animatePresenceInitial?: boolean;
  rotationInterval?: number;
  staggerDuration?: number;
  staggerFrom?: "first" | "last" | "center" | "random" | number;
  loop?: boolean;
  auto?: boolean;
  splitBy?: "characters" | "words" | "lines" | string;
  onNext?: (index: number) => void;
  mainClassName?: string;
  splitLevelClassName?: string;
  elementLevelClassName?: string;
}

const RotatingText = forwardRef<RotatingTextRef, RotatingTextProps>(
  (
    {
      texts,
      transition = { type: "spring", damping: 25, stiffness: 300 },
      initial = { y: "100%", opacity: 0 },
      animate = { y: 0, opacity: 1 },
      exit = { y: "-120%", opacity: 0 },
      animatePresenceMode = "wait",
      animatePresenceInitial = false,
      rotationInterval = 2200,
      staggerDuration = 0.01,
      staggerFrom = "last",
      loop = true,
      auto = true,
      splitBy = "characters",
      onNext,
      mainClassName,
      splitLevelClassName,
      elementLevelClassName,
      ...rest
    },
    ref
  ) => {
    const [currentTextIndex, setCurrentTextIndex] = useState<number>(0);
    const [containerHeight, setContainerHeight] = useState<number | null>(null);
    // Ref for the inner motion.div that wraps the animated text elements
    const innerContainerRef = useRef<HTMLDivElement>(null);

    const splitIntoCharacters = (text: string): string[] => {
      // Fix: Cast Intl to any to access Segmenter and provide explicit types for segmenter output
      // This handles cases where TypeScript's Intl type definition might not include Segmenter.
      if (typeof Intl !== "undefined" && (Intl as any).Segmenter) {
        try {
           const segmenter = new (Intl as any).Segmenter("en", { granularity: "grapheme" });
           // Fix: Corrected the type of the segmenter result to match its usage.
           return Array.from(segmenter.segment(text) as Iterable<{ segment: string }>, (s: { segment: string }) => s.segment);
        } catch (error) {
           // console.error("Intl.Segmenter failed, falling back to simple split:", error);
           return text.split('');
        }
      }
      return text.split('');
    };

    const elements = useMemo(() => {
        const currentText: string = texts[currentTextIndex] ?? '';
        if (splitBy === "characters") {
            const words = currentText.split(/(\s+)/); // Split by spaces, keeping spaces
            let charCount = 0;
            return words.filter(part => part.length > 0).map((part) => {
                const isSpace = /^\s+$/.test(part);
                const chars = isSpace ? [part] : splitIntoCharacters(part);
                const startIndex = charCount;
                charCount += chars.length;
                return { characters: chars, isSpace: isSpace, startIndex: startIndex };
            });
        }
        if (splitBy === "words") {
            return currentText.split(/(\s+)/).filter(word => word.length > 0).map((word, i) => ({ // Keep spaces
                characters: [word], isSpace: /^\s+$/.test(word), startIndex: i
            }));
        }
        if (splitBy === "lines") {
            return currentText.split('\n').map((line, i) => ({
                characters: [line], isSpace: false, startIndex: i
            }));
        }
        // Custom splitter
        return currentText.split(splitBy).map((part, i) => ({
            characters: [part], isSpace: false, startIndex: i
        }));
    }, [texts, currentTextIndex, splitBy]);

    const totalElements = useMemo(() => elements.reduce((sum, el) => sum + el.characters.length, 0), [elements]);

    const getStaggerDelay = useCallback(
      (index: number, total: number): number => {
        if (total <= 1 || !staggerDuration) return 0;
        const stagger = staggerDuration;
        switch (staggerFrom) {
          case "first": return index * stagger;
          case "last": return (total - 1 - index) * stagger;
          case "center":
            const center = (total - 1) / 2;
            return Math.abs(center - index) * stagger;
          case "random": return Math.random() * (total - 1) * stagger;
          default:
            if (typeof staggerFrom === 'number') {
              const fromIndex = Math.max(0, Math.min(staggerFrom, total - 1));
              return Math.abs(fromIndex - index) * stagger;
            }
            return index * stagger; // Default to 'first' if invalid string
        }
      },
      [staggerFrom, staggerDuration]
    );

    const handleIndexChange = useCallback(
      (newIndex: number) => {
        setCurrentTextIndex(newIndex);
        onNext?.(newIndex);
      },
      [onNext]
    );

    const next = useCallback(() => {
      const nextIndex = currentTextIndex === texts.length - 1 ? (loop ? 0 : currentTextIndex) : currentTextIndex + 1;
      if (nextIndex !== currentTextIndex) handleIndexChange(nextIndex);
    }, [currentTextIndex, texts.length, loop, handleIndexChange]);

    const previous = useCallback(() => {
      const prevIndex = currentTextIndex === 0 ? (loop ? texts.length - 1 : currentTextIndex) : currentTextIndex - 1;
      if (prevIndex !== currentTextIndex) handleIndexChange(prevIndex);
    }, [currentTextIndex, texts.length, loop, handleIndexChange]);

    const jumpTo = useCallback(
      (index: number) => {
        const validIndex = Math.max(0, Math.min(index, texts.length - 1));
        if (validIndex !== currentTextIndex) handleIndexChange(validIndex);
      },
      [texts.length, currentTextIndex, handleIndexChange]
    );

     const reset = useCallback(() => {
        if (currentTextIndex !== 0) handleIndexChange(0);
     }, [currentTextIndex, handleIndexChange]);

    useImperativeHandle(ref, () => ({ next, previous, jumpTo, reset }), [next, previous, jumpTo, reset]);

    useEffect(() => {
      if (!auto || texts.length <= 1) return;
      const intervalId = setInterval(next, rotationInterval);
      return () => clearInterval(intervalId);
    }, [next, rotationInterval, auto, texts.length]);

    // Measure the height of the inner container after layout
    useLayoutEffect(() => {
        if (innerContainerRef.current) {
            setContainerHeight(innerContainerRef.current.offsetHeight);
        } else {
            setContainerHeight(null); // Reset if ref is somehow null
        }
    }, [texts, currentTextIndex, splitBy]); // Recalculate on text or split changes


    // Calculate initial and exit variants based on container height
    const calculatedVariants = useMemo(() => {
        const initialAsTarget = typeof initial === 'object' && !Array.isArray(initial) ? initial : null;
        const exitAsTarget = typeof exit === 'object' && !Array.isArray(exit) ? exit : null;

        const initialY = containerHeight !== null && initialAsTarget?.y !== undefined
            ? parseFloat(initialAsTarget.y.toString()) / 100 * containerHeight
            : 0; // Fallback to 0

        const exitY = containerHeight !== null && exitAsTarget?.y !== undefined
            ? parseFloat(exitAsTarget.y.toString()) / 100 * containerHeight
            : 0; // Fallback to 0

        return {
            initial: {
                ...(initialAsTarget || {}), // Include other initial properties
                y: initialY,
            },
            exit: {
                ...(exitAsTarget || {}), // Include other exit properties
                y: exitY,
            },
        };
    }, [containerHeight, initial, exit]);


    return (
      <motion.span
        className={cn(
            "inline-flex flex-wrap whitespace-pre-wrap relative align-bottom",
            // pb-[10px] was in original, adjust if needed based on line-height and desired baseline
            mainClassName
        )}
        {...rest}
        // Do not apply layout here, apply on the inner motion.div
      >
        {/* Screen reader text */}
        <span className="sr-only">{texts[currentTextIndex]}</span>
        <AnimatePresence mode={animatePresenceMode} initial={animatePresenceInitial}>
          {/* This is the container we need to measure and animate */}
          <motion.div
            ref={innerContainerRef} // Assign ref here for measurement
            key={currentTextIndex}
            className={cn(
               "inline-flex flex-wrap relative",
               splitBy === "lines" ? "flex-col items-start w-full" : "flex-row items-baseline",
            )}
            layout // Apply layout animation here
            aria-hidden="true"
            // Remove initial, animate, exit from here - applied to children
          >
             {elements.map((elementObj, elementIndex) => (
                // This span wraps a word or a space
                <span
                    key={elementIndex}
                    className={cn("inline-flex", splitBy === 'lines' ? 'w-full' : '', splitLevelClassName)}
                    style={{ whiteSpace: 'pre' }} // Preserves spaces within the word/space part
                >
                    {elementObj.characters.map((char, charIndex) => {
                        const globalIndex = elementObj.startIndex + charIndex;
                        return (
                            <motion.span
                                key={`${char}-${charIndex}`}
                                // Use calculated initial and exit y based on containerHeight
                                initial={calculatedVariants.initial}
                                animate={animate} // Use the defined animate prop
                                exit={calculatedVariants.exit}
                                transition={{
                                    ...transition,
                                    delay: getStaggerDelay(globalIndex, totalElements),
                                }}
                                className={cn("inline-block leading-none tracking-tight", elementLevelClassName)} // leading-none can be adjusted
                            >
                                {char === ' ' ? '\u00A0' : char}
                            </motion.span>
                        );
                     })}
                </span>
              ))}
          </motion.div>
        </AnimatePresence>
      </motion.span>
    );
  }
);
RotatingText.displayName = "RotatingText";
export default RotatingText;
