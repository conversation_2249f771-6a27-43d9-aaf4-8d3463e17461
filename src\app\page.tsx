import { getServe<PERSON><PERSON>ogger } from '@/lib/logger';
import { getServerAuthWithProfilePrefetch } from '@/lib/auth/server-auth.utils';
import { HydrationBoundary } from '@tanstack/react-query';
import { HomepageLayout } from '@/features/homepage/layout/homepage-layout';

const logger = getServerLogger('RootPage');

/**
 * Optimized root page component
 * Uses centralized auth utility for efficient data prefetching and consistent error handling
 */
export default async function RootPage() {
  try {
    // Use centralized server auth utility (follows DRY principle)
    const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
      prefetchProfile: true, // Prefetch profile if user is authenticated
      profileTimeout: 500,
      profileStaleTime: 10 * 1000,
      requestId: `root-${Date.now()}`,
      debugMode: process.env.NODE_ENV === 'development'
    });
    
    if (error) {
      // Error already logged in centralized utility, just add context
      logger.warn('Auth error in root page', {
        error: error.message,
        operation: 'RootPage'
      });
    }
    
    if (user?.id) {
      // Mask userId for privacy in logs
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      
      logger.info('Root page loaded for authenticated user', {
        userId: maskedUserId,
        operation: 'RootPage'
      });
    } else {
      logger.info('Root page loaded for anonymous user', {
        operation: 'RootPage'
      });
    }

    return (
      <HydrationBoundary state={dehydratedState}>
        <HomepageLayout />
      </HydrationBoundary>
    );
  } catch (err) {
    logger.error('Critical error in root page', {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      operation: 'RootPage'
    });
    
    // Fallback to basic layout without hydrated state
    return <HomepageLayout />;
  }
}

