# Product Requirements Document: Create Recipe Final Step

## 1. Introduction/Overview

The Create Recipe Final Step is the culminating component of the Essential Oil Recipe Creator feature that transforms collected user data from the 5-step wizard into three personalized, time-specific essential oil protocols. This step replaces the unused "oils" step and provides users with actionable, professionally-structured recipes they can immediately implement.

**Problem Statement:** Users complete the comprehensive 5-step analysis (health concern, demographics, causes, symptoms, therapeutic properties) but lack concrete, safe, and professionally-structured recipes with precise measurements and usage instructions for their daily routine.

**Goal:** Generate three parallel, time-specific essential oil protocols (morning, mid-day, night) with exact measurements, safety guidelines, container recommendations, and comprehensive usage instructions based on the user's complete health profile.

## 2. Goals

- **Primary Goal:** Replace the unused "oils" step with a dynamic final step that generates 3 AI-powered recipes in parallel using OpenAI Agents JS SDK
- **Safety Goal:** Ensure 100% safety compliance by filtering dermocaustic oils for children under 10 and applying conservative dilution ratios based on demographic data
- **Usability Goal:** Deliver clear, actionable protocols with exact measurements, container recommendations, and application instructions suitable for novice users
- **Performance Goal:** Implement parallel AI generation with streaming responses to deliver recipes efficiently
- **Integration Goal:** Seamlessly integrate into existing create-recipe workflow without breaking changes, maintaining sequential step numbering

## 3. User Stories

- **As a user**, I want to see my complete health profile analysis so I understand the therapeutic strategy behind my recipe
- **As a user**, I want three time-specific recipes (morning/mid-day/night) with exact measurements so I can prepare them safely and follow a daily protocol
- **As a user**, I want detailed container recommendations (size, type, dispenser) so I know exactly what to purchase and how to prepare my recipes
- **As a user**, I want to understand why specific oils were selected through brief rationale explanations so I can trust the recommendations
- **As a user**, I want safety warnings specific to my demographic profile so I can use the oils safely
- **As a user**, I want application instructions with timing and frequency so I can follow a professional protocol

## 4. Functional Requirements

### 4.1 Data Input & Processing
- **FR-1:** System must receive user data matching `docs/create-recipe/new-final-step/json-dataset-minimal.json` structure plus time-of-day variable for each recipe generation
- **FR-2:** System must transform collected health profile, demographics, and preferences into AI-ready format
- **FR-3:** System must validate data completeness before proceeding (impossible to reach final step without complete data due to DEV button validation)

### 4.2 AI Recipe Generation System
- **FR-4:** System must use OpenAI Agents JS SDK with `gpt-4.1-nano` model following existing patterns in `src/lib/ai`
- **FR-5:** System must generate 3 distinct recipes in parallel with same prompt template, passing different time-of-day variables (morning/mid-day/night)
- **FR-6:** System must filter dermocaustic oils for users under 10 years old based on age category and specific age from demographics
- **FR-7:** System must use prompt template based on `docs/create-recipe/new-final-step/08 - synergistic-oils.yaml` with dynamic safety data, causes, symptoms, and oils
- **FR-8:** System must generate short rationale sentences for oil selection, building upon existing dataset rationale
- **FR-9:** System must select 3-6 oils from candidate list for optimal synergy and coverage
- **FR-10:** System must calculate container size based on drops per application × frequency × duration, considering application area (face vs body parts)

### 4.3 Dynamic Frontend Generation
- **FR-11:** System must generate dynamic UI based on AI-produced JSON schema following `docs/create-recipe/new-final-step/standalone-v1.html` layout
- **FR-12:** System must use existing theme variables only (no hardcoded custom styles/colors)
- **FR-13:** System must display all mandatory fields from standalone-v1.html mockup except "Segurança" tab (static content)
- **FR-14:** System must provide specific container recommendations including size (ml), container type, and dispenser type based on recipe usage
- **FR-15:** System must recommend materials (amber glass, clear glass, plastic) based on oil/synergy conservation properties

### 4.4 Error Handling & Retry Logic
- **FR-16:** System must automatically retry failed recipe generation up to 2 times per recipe
- **FR-17:** System must display successful recipes while showing error messages for failed recipes after retry attempts
- **FR-18:** System must implement streaming responses for progressive loading (Phase 2 loading features)

### 4.5 Navigation & State Management
- **FR-19:** System must prevent navigation back from final step (no changes allowed due to generation cost)
- **FR-20:** System must maintain sequential step numbering in workflow
- **FR-21:** System must store final step data appropriately within existing state management architecture

## 5. Non-Goals (Out of Scope)

- **Recipe Regeneration:** Users cannot regenerate recipes due to AI token costs (one-time generation per session)
- **Recipe Editing:** User editing capabilities planned for Phase 2
- **Database Persistence:** Recipe saving to user profile planned for Phase 2  
- **Oil Substitution:** Alternative oil recommendations planned for Phase 3
- **Advanced Features:** Scientific studies integration, oil profiles, dilution calculator, wellness tabs planned for future phases

## 6. Design Considerations

- **UI Reference:** Primary design follows `docs/create-recipe/new-final-step/standalone-v1.html`
- **Mobile-First:** Responsive design for 90% mobile user base
- **Component Architecture:** Modular components under 500 lines following DRY and KISS principles
- **Consistency:** Visual and interactive consistency with existing create-recipe workflow steps

## 7. Technical Considerations

- **Integration:** Replace RecipeStep.OILS enum and navigation logic
- **State Management:** Integrate with existing Zustand store, accessing previous step data (oil IDs, property IDs, etc.)
- **Streaming Architecture:** Implement streaming responses using existing patterns
- **Component Structure:** Break down into smaller components (RecipeCard, ContainerRecommendation, SafetyWarnings) following 500-line limit
- **Mock Data:** Include test data matching json-dataset-minimal.json + time-of-day variable for development

## 8. Success Metrics

- **Technical:** 100% test coverage, JSON schema validation for AI responses, zero breaking changes to existing workflow
- **Performance:** Efficient parallel recipe generation with streaming responses
- **Safety:** 100% accuracy in demographic-based oil filtering and dilution calculations
- **Internationalization:** Complete i18n implementation with _localized keys for all user-facing text
- **User Experience:** Responsive design matching standalone-v1.html mockup across all devices

## 9. Open Questions

*All clarifying questions have been addressed through the discovery process. Implementation can proceed based on the requirements outlined above.*

---

**Document Version:** 3.0  
**Created:** 2025-01-14  
**Target Audience:** Junior Developer  
**Estimated Development Time:** 2-3 sprints
