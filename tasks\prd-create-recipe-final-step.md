# Product Requirements Document: Create Recipe Final Step

## 1. Introduction/Overview

The Create Recipe Final Step is the culminating component of the Essential Oil Recipe Creator feature. This step transforms the collected user data from the 5-step wizard (health concern, demographics, causes, symptoms, and therapeutic properties) into personalized, actionable essential oil protocols for morning, mid-day, and night usage.

**Problem Statement:** Users complete the 5-step analysis but need concrete, safe, and professionally-structured recipes they can immediately implement in their daily routine.

**Goal:** Generate three time-specific essential oil protocols (morning, mid-day, night) with precise measurements, safety guidelines, and comprehensive usage instructions based on the user's complete health profile.

## 2. Goals

1. **Primary Goal:** Replace the unused "oils" step with a comprehensive final step that generates 3 parallel AI-powered recipes
2. **Safety Goal:** Ensure 100% safety compliance by filtering dermocaustic oils for children and applying appropriate dilution ratios
3. **Usability Goal:** Provide clear, actionable protocols with exact measurements and step-by-step instructions
4. **Technical Goal:** Implement parallel AI processing for sub-3-second recipe generation
5. **Integration Goal:** Seamlessly integrate with existing create-recipe workflow without breaking changes

## 3. User Stories

### Core Professional User Stories
- **As a user**, I want to see my complete health profile analysis so I understand the therapeutic strategy behind my recipe
- **As a user**, I want a complete recipe with exact measurements so I can prepare it safely and accurately  
- **As a user**, I want detailed usage instructions with timing so I can follow a professional protocol
- **As a user**, I want to know exactly what container to buy and how to prepare it so I can create the recipe properly

### Advanced User Stories  
- **As a user**, I want to see the root cause analysis so I understand why certain oils were selected
- **As a user**, I want carrier oil information and dilution guidance for safe application
- **As a user**, I want time-specific protocols (morning/mid-day/night) that complement my daily rhythm
- **As a user**, I want safety warnings specific to my demographic profile (age, pregnancy status)

## 4. Functional Requirements

### 4.1 Data Input Processing
1. **FR-1:** System must receive and process user data matching the structure in `docs/create-recipe/new-final-step/json-dataset-minimal.json`
2. **FR-2:** System must transform collected health profile, demographics, and preferences into AI-ready format
3. **FR-3:** System must validate data completeness before proceeding to AI generation

### 4.2 AI Recipe Generation System  
4. **FR-4:** System must implement OpenAI Agents JS SDK following existing patterns in `src/lib/ai`
5. **FR-5:** System must generate 3 distinct recipes simultaneously using parallel processing:
   - Morning protocol/recipe
   - Mid-day protocol/recipe  
   - Night protocol/recipe
6. **FR-6:** System must filter out dermocaustic oils when demographics indicate child user
7. **FR-7:** System must use prompt template based on `docs/create-recipe/new-final-step/08 - synergistic-oils.yaml`
8. **FR-8:** System must select 3-6 oils from candidate list for optimal synergy and coverage
9. **FR-9:** System must calculate safe dilution ratios based on most restrictive oil requirements

### 4.3 Dynamic Frontend Generation
10. **FR-10:** System must generate dynamic UI following design mockup in `docs/create-recipe/new-final-step/standalone-v1.html`
11. **FR-11:** System must use theme variables only (no hardcoded custom styles/colors)
12. **FR-12:** System must dynamically create frontend fields based on AI-generated JSON schema
13. **FR-13:** System must display required sections:
    - Container & Dispensing System recommendations
    - Usage modes (main and alternative)  
    - Exact measurements and dilution ratios
    - Timing and frequency protocols
    - Root cause analysis and oil selection rationale
    - Carrier oil recommendations

### 4.4 Safety and Compliance
14. **FR-14:** System must implement comprehensive safety filtering based on user demographics
15. **FR-15:** System must display appropriate safety warnings for each recipe
16. **FR-16:** System must calculate conservative dilution percentages for children (0.5-1%)
17. **FR-17:** System must include phototoxicity warnings when applicable

### 4.5 Integration and Navigation
18. **FR-18:** System must replace the unused "oils" step in existing workflow
19. **FR-19:** System must maintain user session data through final step
20. **FR-20:** System must provide navigation back to previous steps
21. **FR-21:** System must implement auto-save functionality consistent with existing steps

## 5. Non-Goals (Out of Scope)

- **Oil substitution functionality** (reload button for alternative oils) - Phase 2
- **Scientific studies integration** with `docs/own-mcp` - Phase 3  
- **Complete oil profiles** via Hasura GraphQL - Phase 3
- **Interactive dilution calculator** - Phase 3
- **Holistic wellness tabs** (affirmations, lifestyle modifications) - Phase 2
- **Direct API calls** (must use OpenAI Agents JS SDK)
- **Breaking changes** to existing workflow steps

## 6. Design Considerations

### UI/UX Requirements
- **Mobile-first responsive design** (90% mobile users)
- **Follow existing theme variables** from current create-recipe components
- **Implement design patterns** from `docs/create-recipe/new-final-step/standalone-v1.html`
- **Maintain visual consistency** with existing wizard steps
- **Support expandable/collapsible sections** for detailed information

### Component Architecture
- **Reuse existing patterns** from `src/features/create-recipe/components`
- **Follow barrel file structure** for imports
- **Implement proper TypeScript interfaces** for all data structures
- **Use React Hook Form** for any form interactions
- **Integrate with existing Zustand store** patterns

## 7. Technical Considerations

### Technology Stack
- **OpenAI Agents JS SDK** (mandatory - no direct API calls)
- **Parallel API processing** following existing patterns in other steps
- **TypeScript** with strict type safety
- **React Hook Form** for form management  
- **Zustand** for state management integration
- **Advanced streaming architecture** with DRY principles

### Integration Points
- **Replace RecipeStep.OILS** in existing enum and navigation
- **Extend existing store** with final step state management
- **Follow existing prompt structure** in `src/features/create-recipe/prompts/`
- **Implement consistent error handling** with existing steps
- **Maintain existing auto-save patterns**

### Performance Requirements
- **Sub-3-second parallel recipe generation**
- **Optimized loading states** during AI processing
- **Efficient state management** to prevent unnecessary re-renders
- **Progressive enhancement** for slower connections

## 8. Success Metrics

### Technical Metrics
- **100% test coverage** for AI integration and recipe generation
- **Sub-3-second response time** for parallel recipe generation
- **Zero breaking changes** to existing workflow
- **100% TypeScript compliance** with strict mode

### User Experience Metrics  
- **Mobile-responsive design** passing all viewport tests
- **Complete i18n implementation** for en/es/pt languages
- **Intuitive UX** with clear visual hierarchy
- **Accessible design** meeting WCAG guidelines

### Safety and Compliance Metrics
- **100% safety filtering accuracy** for demographic-based restrictions
- **Correct dilution calculations** for all age groups
- **Comprehensive safety warnings** display
- **Zero safety-related user reports**

## 9. Open Questions

1. **State Management:** Should the final step data be persisted in localStorage or remain session-only like other steps?
2. **Error Handling:** What fallback behavior should occur if AI generation fails for one or more time periods?
3. **Validation:** Should there be user confirmation/editing capabilities for generated recipes before final display?
4. **Navigation:** Should users be able to regenerate recipes or is it a one-time generation per session?
5. **Internationalization:** Are there specific cultural considerations for recipe timing (morning/mid-day/night) across different locales?

---

**Document Version:** 1.0  
**Created:** 2025-01-14  
**Target Audience:** Junior Developer  
**Estimated Development Time:** 2-3 sprints
