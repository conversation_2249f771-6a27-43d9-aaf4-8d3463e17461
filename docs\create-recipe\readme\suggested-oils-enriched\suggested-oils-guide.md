# Suggested Oils Frontend Implementation Guide

## Data Flow Overview

### 1. Initial Data Reception
When the AI suggests oils for therapeutic properties, the data flows through several stages:

1. **API Response Reception**
   - Frontend receives `SuggestedOilsResponse` through streaming API endpoint `/api/ai/streaming`
   - Response contains:
     - Therapeutic property context (ID, names, descriptions)
     - Array of suggested oils (basic information)
   - Data is validated against `suggestedOilsResponseSchema` (Zod schema)

2. **Streaming Process** (`src/features/create-recipe/hooks/use-create-recipe-streaming.ts`)
   - Uses `useParallelStreamingEngine` for handling multiple property streams
   - Each property gets its own stream for oil suggestions
   - Streams are processed in parallel for better performance

3. **Data Enrichment** (`src/lib/ai/services/batch-enrichment.service.ts`)
   - Property-Level Processing:
     * Each property independently manages its own enrichment process
     * When a property receives suggested oils, all its oils are enriched in a single batch
     * One OpenAI API call per property for generating embeddings
     * One vector search query per property for safety data retrieval
   - Enrichment Steps:
     a. Format search queries from oil names
     b. Generate batch embeddings for all oils in the property
     c. Perform vector similarity search in Supabase
     d. Enrich oils with safety information

### 2. Core Data Structures

#### Therapeutic Property Context
```typescript
{
  'therapeutic_property_context': {
    'property_id': 'uuid',          // Unique identifier
    'property_name_localized': 'string',    // User's language
    'property_name_english': 'string',      // English name
    'description_localized': 'string'       // User's language
  }
}
```

#### Essential Oil Structure
```typescript
{
  // Basic Oil Information
  'oil_id': 'uuid',                // Unique identifier
  'name_english': 'string',        // English name
  'name_botanical': 'string',      // Scientific name
  'name_localized': 'string',      // User's language
  
  // Matching Information
  'match_rationale_localized': 'string',    // Why suggested
  'relevancy_to_property_score': number,    // Score (1-5)
  
  // Enrichment Data
  'supabase_id': 'string',        // Database reference
  'description': 'string',        // Detailed description
  'safety': {
    'internal_use': {
      'name': string | null,
      'code': string | null,
      'description': string | null,
      'guidance': string | null
    },
    'dilution': {
      'name': string | null,
      'description': string | null,
      'percentage_max': number | null,
      'percentage_min': number | null,
      'ratio': string | null
  },
    'phototoxicity': {
      'status': string | null,
      'guidance': string | null,
      'description': string | null
    },
    'pregnancy_nursing': Array<{
      'id': string | null,
      'name': string | null,
      'status_description': string | null,
      'code': string | null,
      'usage_guidance': string | null,
      'description': string | null
    }>,
    'child_safety': Array<{
      'age_range': string | null,
      'safety_notes': string | null
    }>
  },
  'isEnriched': boolean          // Enrichment status
}
```

### 3. Data Processing Pipeline

#### A. Initial Property Processing
1. **Auto-Trigger Mechanism** (`properties-display.tsx`)
   ```typescript
   useEffect(() => {
     if (!shouldAutoAnalyzeProperties || autoTriggerExecutedRef.current) return;
     
     const hasPropertiesWithoutOils = therapeuticProperties.some(p => !p.suggested_oils?.length);
     const hasRequiredData = healthConcern && demographics && therapeuticProperties.length > 0;
     const canAnalyze = hasPropertiesWithoutOils && hasRequiredData && !parallelStreamingState.isStreaming;
     
     if (canAnalyze) {
       autoTriggerExecutedRef.current = true;
       setShouldAutoAnalyzeProperties(false);
       handleAnalyzeAllProperties();
     }
   });
   ```

2. **Property Analysis Trigger**
   ```typescript
   const handleAnalyzeAllProperties = async () => {
     // Reset previous state
     resetParallelStreams();
     
     // Start parallel streaming
     await startOilSuggestionStreaming(
       therapeuticProperties,
       healthConcern,
       demographics,
       selectedCauses,
       selectedSymptoms,
       userLanguage
     );

     // Update UI loading state
     const loadingProperties = therapeuticProperties.map(p => ({
       ...p,
       isLoadingOils: true,
       errorLoadingOils: null,
     }));

     updateTherapeuticProperties(loadingProperties, 'parallelStreaming (Start)');
   };
   ```

#### B. Streaming Process
1. **Request Creation** (`use-create-recipe-streaming.ts`)
   ```typescript
   const requests: ParallelStreamRequest[] = properties.map(property => ({
     id: property.property_id,
     url: '/api/ai/streaming',
     requestData: createStreamRequest(
       'create-recipe',
       'suggested-oils',
       healthConcern,
       demographics,
       selectedCauses,
       selectedSymptoms,
       userLanguage,
       property
     ),
     label: property.property_name,
     responseParser: (updates) => {
       if (updates.finalData?.data?.suggested_oils) {
         return {
           therapeutic_property_context: updates.finalData.data.therapeutic_property_context,
           suggested_oils: updates.finalData.data.suggested_oils
         };
       }
       return null;
     }
   }));
   ```

2. **Data Transformation** (`api-data-transform.ts`)
   ```typescript
   export function createStreamRequest(
     feature: string,
     step: string,
     healthConcern: HealthConcernData,
     demographics: DemographicsData,
     selectedCauses: PotentialCause[],
     selectedSymptoms: PotentialSymptom[],
     userLanguage: string,
     property?: TherapeuticProperty
   ): StreamRequest {
     const data = {
       health_concern: healthConcern.healthConcern.trim(),
       gender: demographics.gender,
       age_category: demographics.ageCategory,
       age_specific: demographics.specificAge.toString(),
       user_language: userLanguage,
       selected_causes: selectedCauses,
       selected_symptoms: selectedSymptoms
     };

     if (property) {
       if (step === 'oil-enrichment' && property.suggested_oils) {
         return {
           feature,
           step,
           data: {
             ...data,
             therapeutic_property: {
               property_id: property.property_id,
               property_name: property.property_name,
               property_name_english: property.property_name_english || property.property_name,
               description: property.description
             },
             suggested_oils: property.suggested_oils
           }
         };
       }
       
       return {
         feature,
         step,
         data: {
           ...data,
           therapeutic_property: property
         }
       };
     }

     return { feature, step, data };
   }
   ```

#### C. Enrichment Process

1. **Enrichment Trigger Flow** (`properties-display.tsx`)
   The enrichment process is triggered independently for each therapeutic property when it receives its suggested oils. Here's how it works:

   ```typescript
   // Inside handleStreamingResults callback
   const updatedProperties = currentProperties.map(p => {
     // Check if this property has received streaming results
     if (parallelStreamingState.results.has(p.property_id)) {
       const result = parallelStreamingState.results.get(p.property_id);
       const suggested_oils = result?.suggested_oils.map((oil: EssentialOil) => ({
         ...oil,
         isEnriched: oil.isEnriched ?? false
       })) || [];
       
       // MVP Enrichment Check:
       // 1. We have oils to process
       // 2. At least one oil has never been through enrichment
       // 3. Property isn't currently being enriched
       const hasUnprocessedOils = suggested_oils.some(oil => !oil.enrichment_status);

       if (suggested_oils.length > 0 && 
           hasUnprocessedOils && 
           propertyEnrichmentStatus[p.property_id] !== 'loading') {
         
         setPropertyEnrichmentStatus(p.property_id, 'loading');
         triggerBatchEnrichment(p.property_id, suggested_oils);
       }
       return {
         ...p,
         suggested_oils,
         isLoadingOils: false,
         errorLoadingOils: suggested_oils.length > 0 ? null : 'No suggestions found.',
         // Consider property enriched if all oils have been processed (including not_found)
         isEnriched: suggested_oils.every(oil => !!oil.enrichment_status)
       };
     }
     return p;
   });
   ```

   **MVP Triple-Check System:**
   1. `suggested_oils.length > 0`
      - Ensures we have oils to process
      - Prevents unnecessary API calls
      - Quick initial validation

   2. `hasUnprocessedOils` check:
      - Only triggers for oils that have never been processed (!enrichment_status)
      - Treats all processed states as valid end states:
        * 'enriched' - Successfully matched with safety data
        * 'not_found' - AI hallucination, invalid oil (this is okay!)
        * 'discarded' - Low similarity match
      - No retries - one enrichment attempt per oil is sufficient

   3. `propertyEnrichmentStatus[p.property_id] !== 'loading'`
      - Prevents concurrent enrichment requests
      - Maintains process integrity
      - Avoids race conditions

   This MVP approach:
   - Validates AI suggestions through enrichment
   - Treats 'not_found' as a valid end state (AI hallucination caught)
   - Only processes each oil once
   - Maintains simple, clear validation logic

2. **Batch Enrichment Service** (`batch-enrichment.service.ts`)
   When the service receives oils for enrichment, it processes them efficiently:

   ```typescript
   async batchEnrichOils(suggestedOils: SuggestedOilData[]): Promise<BatchEnrichmentResponse> {
     // Step 1: Format search queries for ALL oils in the batch
     const searchQueries = suggestedOils.map(oil => 
       `${oil.name_english} - ${oil.name_botanical}`.trim()
     );

     // Step 2: Generate embeddings for ALL oils in a single API call
     const embeddingsResults = await this.batchEmbeddingService.createBatchEmbeddings({
       texts: searchQueries
     });

     // Step 3: Perform batch vector search for ALL oils at once
     const { data: searchResults } = await supabase.rpc('batch_similarity_search', {
       query_embeddings: embeddings,
       match_threshold: 0.5,
       match_count: 3,
     });

     // Step 4: Process results and return enriched oils
     const enrichedOils = await this.processSearchResults(
       suggestedOils, 
       searchQueries, 
       searchResults
     );

     return {
       enriched_oils: enrichedOils,
       total_input: suggestedOils.length,
       ...this.calculateStats(enrichedOils),
       processing_time_ms: Math.round(performance.now() - startTime)
     };
   }
   ```

   Performance Benefits:
   - Single OpenAI API call for all oils in the batch
   - One database query for vector similarity search
   - Parallel processing of search results
   - Efficient state updates in the frontend

3. **Result Processing Details** (`batch-enrichment.service.ts`)
   The `processSearchResults` function handles the detailed enrichment of each oil:

   ```typescript
   private async processSearchResults(
     suggestedOils: SuggestedOilData[],
     searchQueries: string[],
     searchResults: any[]
   ): Promise<EnrichedOilData[]> {
     // Group results by embedding_index for efficient lookup
     const resultsByIndex = searchResults.reduce((acc: any, result: any) => {
       const index = result.embedding_index;
       if (!acc[index]) acc[index] = [];
       acc[index].push(result);
       return acc;
     }, {});

     // Process each oil individually
     for (let i = 0; i < suggestedOils.length; i++) {
       const oil = suggestedOils[i];
       const embeddingIndex = i + 1; // Supabase uses 1-based indexing
       const results = resultsByIndex[embeddingIndex] || [];
       const searchQuery = searchQueries[i];

       // Three possible outcomes for each oil:
       // 1. Not Found - No vector search results
       // 2. Discarded - Low similarity + botanical mismatch
       // 3. Enriched - Successfully matched and enriched
       ...
     }
   }
   ```

   Key Processing Steps:
   
   a. **Result Organization**
      - Results are grouped by embedding index for efficient lookup
      - Each oil's results are processed independently
      - Maintains 1:1 relationship between queries and results

   b. **Validation and Status Assignment**
      - Each oil can have one of three statuses:
        1. **Not Found** (`enrichment_status: 'not_found'`)
           ```typescript
           if (results.length === 0) {
             return {
               ...oil,
               enrichment_status: 'not_found',
               search_query: searchQuery,
               enrichment_timestamp: new Date().toISOString(),
               isEnriched: false
             };
           }
           ```
        
        2. **Discarded** (`enrichment_status: 'discarded'`)
           - Occurs when:
             * Similarity score < 0.65 AND
             * Botanical name mismatch detected
           ```typescript
           if (similarity < 0.65 && botanicalMismatch) {
             return {
               ...oil,
               enrichment_status: 'discarded',
               botanical_mismatch: true,
               similarity_score: similarity,
               search_query: searchQuery,
               enrichment_timestamp: new Date().toISOString(),
               isEnriched: false
             };
           }
           ```
        
        3. **Enriched** (`enrichment_status: 'enriched'`)
           - Successfully matched and enriched with safety data
           ```typescript
           return {
             ...oil,
             name_scientific: bestMatch.name_scientific,
             safety: {
               internal_use: bestMatch.internal_use,
               dilution: bestMatch.dilution,
               phototoxicity: bestMatch.phototoxicity,
               pregnancy_nursing: bestMatch.pregnancy_nursing_safety,
               child_safety: bestMatch.child_safety
             },
             enrichment_status: 'enriched',
             botanical_mismatch: botanicalMismatch,
             similarity_score: similarity,
             search_query: searchQuery,
             enrichment_timestamp: new Date().toISOString(),
             isEnriched: true
           };
           ```

   c. **Safety Data Structure**
      When an oil is successfully enriched, it receives comprehensive safety information:
      ```typescript
      interface OilSafetyData {
        internal_use: {
          name: string | null;
          code: string | null;
          description: string | null;
          guidance: string | null;
        };
        dilution: {
          name: string | null;
          description: string | null;
          percentage_max: number | null;
          percentage_min: number | null;
          ratio: string | null;
        };
        phototoxicity: {
          status: string | null;
          guidance: string | null;
          description: string | null;
        };
        pregnancy_nursing: Array<{
          id: string | null;
          name: string | null;
          status_description: string | null;
          code: string | null;
          usage_guidance: string | null;
          description: string | null;
        }>;
        child_safety: Array<{
          age_range: string | null;
          safety_notes: string | null;
        }>;
      }
      ```

   d. **Validation Process Details**
      The enrichment process includes a robust validation system to ensure data quality and safety. This is particularly important for botanical names, where the AI might suggest variations or incorrect names for essential oils that are commonly known by different names:

      1. **Search Query Formation**
         ```typescript
         const searchQueries = suggestedOils.map(oil => 
           `${oil.name_english} - ${oil.name_botanical}`.trim()
         );
         ```
         - Combines English and botanical names for search matching
         - AI-suggested botanical name is used ONLY for search purposes
         - Ensures we can find the correct oil even if AI suggests a variant name

      2. **Vector Search Parameters**
         ```typescript
         const { data: searchResults } = await supabase.rpc('batch_similarity_search', {
           query_embeddings: embeddings,
           match_threshold: 0.5,  // Base threshold for initial results
           match_count: 3,        // Get top 3 matches for potential fallback
         });
         ```
         - Uses cosine similarity for vector matching
         - Initial threshold of 0.5 to cast a wide net
         - Retrieves multiple matches for validation

      3. **Enrichment Status Validation**
         All three enrichment outcomes are considered valid and successful results:
         ```typescript
         // In properties-display.tsx
         const allOilsProcessed = data.enriched_oils.every((oil: EnrichedEssentialOil) => 
           oil.enrichment_status === 'enriched' || 
           oil.enrichment_status === 'not_found' || 
           oil.enrichment_status === 'discarded'
         );
         setPropertyEnrichmentStatus(p.property_id, allOilsProcessed ? 'success' : 'error');
         ```

         a. **Enriched** (`enrichment_status: 'enriched'`)
            - Successfully matched with safety data
            - High similarity score OR exact botanical match
            - Complete safety data available

         b. **Not Found** (`enrichment_status: 'not_found'`)
            - AI suggestion not found in our database
            - Valid outcome indicating AI hallucination
            - Helps identify gaps in AI training

         c. **Discarded** (`enrichment_status: 'discarded'`)
            - Found but failed validation criteria
            - Low similarity (< 0.65) AND botanical mismatch
            - Valid outcome for quality control

         Each status represents a successful processing outcome, not an error state.
         The property is considered successfully enriched when all its oils have been
         processed into any of these three states.

      4. **Botanical Name Validation**
         ```typescript
         private checkBotanicalMismatch(searchedBotanical: string, foundBotanical: string | undefined): boolean {
           if (!foundBotanical) return true;  // Fail safe if no botanical name found
           const trimmedSearched = searchedBotanical.trim().toLowerCase();
           const trimmedFound = foundBotanical.trim().toLowerCase();
           return trimmedSearched !== trimmedFound;
         }
         ```
         - Case-insensitive comparison between AI suggestion and database name
         - Used to track mismatches for internal purposes
         - Does not affect display - database name always takes precedence

      5. **Multi-Factor Validation**
         ```typescript
         // Combined similarity and botanical name check
         if (similarity < 0.65 && botanicalMismatch) {
           return {
             ...oil,
             enrichment_status: 'discarded',
             botanical_mismatch: true,
             similarity_score: similarity,
             search_query: searchQuery,
             enrichment_timestamp: new Date().toISOString(),
             isEnriched: false
           };
         }
         ```
         - Only discards results with BOTH low similarity AND botanical mismatch
         - High similarity matches are enriched even with botanical name differences
         - Preserves the validated database name for display

      6. **Data Structure**
         ```typescript
         interface EnrichedEssentialOil extends EssentialOil {
           supabase_id?: string;
           name_scientific?: string;  // Scientific name from safety database
           description?: string;
           safety?: OilSafetyInfo;
           isEnriched: boolean;
           botanical_mismatch?: boolean;  // Internal tracking only
           similarity_score?: number;
         }
         ```
         - `name_scientific`: The validated botanical name from our safety database
         - `name_botanical`: Used only for search matching, not for display
         - `botanical_mismatch`: Internal flag for tracking purposes
         - `similarity_score`: Confidence measure of the match

      7. **Frontend Display**
         ```typescript
         <div className="font-medium text-foreground">
           {oil.name_localized || oil.name_english}
         </div>
         {oil.name_scientific && (
           <div className="text-sm italic text-muted-foreground">
             {oil.name_scientific}
           </div>
         )}
         ```
         - Always displays the validated scientific name from our database
         - Never shows the AI-suggested botanical name
         - No warnings needed as this is the intended validation flow
         - Ensures users always see correct, validated botanical names

      8. **Validation Outcomes**
         Three possible states after validation:
         - **Not Found** (no vector search results)
           * No matches found in database
           * Immediate rejection without further validation
         - **Discarded** (failed validation)
           * Low similarity (< 0.65) AND botanical name mismatch
           * Records both conditions for analysis
         - **Enriched** (passed validation)
           * High similarity OR exact botanical match
           * Uses database scientific name for display
           * Full safety data enrichment
           * Metadata preserved for traceability

   e. **Quality Control Measures**
      - Scientific names from validated safety database take precedence
      - AI-suggested botanical names used only for search matching
      - Similarity score thresholds (0.65 minimum for acceptance)
      - Timestamp tracking for enrichment process
      - Detailed status tracking for each oil

   f. **Performance Optimizations**
      - Results pre-grouped by index to avoid repeated searches
      - Single-pass processing of each oil
      - Efficient data structure updates
      - Minimal object creation and copying

#### D. State Management
1. **Property States**
   - `isLoadingOils`: Loading state during suggestion streaming
   - `errorLoadingOils`: Error state for suggestion failures
   - `isEnriched`: True when all oils have been processed (any valid status)
   - `enrichment_status`: One of three valid outcomes:
     * `'enriched'` - Successfully matched with safety data
     * `'not_found'` - AI suggestion not in database (valid outcome)
     * `'discarded'` - Failed validation criteria (valid outcome)

2. **Store Updates**
   ```typescript
   // Update property with enriched oils
   updatePropertyWithEnrichedOils(propertyId: string, enrichedOils: EnrichedEssentialOil[]) {
     const updatedProperties = therapeuticProperties.map(p => {
       if (p.property_id === propertyId) {
         // Consider an oil enriched if it has been processed (has any valid status)
         const updatedOils = enrichedOils.map(oil => ({
           ...oil,
           isEnriched: !!oil.enrichment_status
         }));

         // Property is enriched when all oils have been processed
         const allOilsProcessed = updatedOils.every(oil => !!oil.enrichment_status);

         return {
           ...p,
           suggested_oils: updatedOils,
           isEnriched: allOilsProcessed
         };
       }
       return p;
     });

     // Log enrichment statistics for all valid outcomes
     console.log(`✅ [recipe-store] Updated property ${propertyId} with ${enrichedOils.length} oils:`, {
       enriched: enrichedOils.filter(o => o.enrichment_status === 'enriched').length,
       not_found: enrichedOils.filter(o => o.enrichment_status === 'not_found').length,
       discarded: enrichedOils.filter(o => o.enrichment_status === 'discarded').length
     });

     updateTherapeuticProperties(updatedProperties, 'enrichment');
   }

   // Update enrichment status - success means all oils processed to any valid status
   setPropertyEnrichmentStatus(propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') {
     propertyEnrichmentStatus[propertyId] = status;
   }
   ```

##### Crucial: State Loss Prevention in updateTherapeuticProperties

> **New Implementation (2024-06):**
>
> To prevent accidental loss of enrichment data, the `updateTherapeuticProperties` function now merges incoming property updates with the current state, rather than replacing the entire array. If a property in the current state is already enriched (i.e., has `isEnriched: true` and non-empty `suggested_oils`), it will NOT be overwritten by an unenriched or empty version from a batch update. This ensures that enrichment progress is never lost due to a stale or partial update from streaming or re-initialization events.
>
> **Why is this critical?**
> - Previously, a batch update (e.g., from streaming start or error recovery) could overwrite all enrichment progress by replacing the state with a fresh array lacking enrichment results. This led to properties "losing" their oils and enrichment status.
> - Now, the merge logic guarantees that once a property is enriched, it remains so unless explicitly reset. This preserves user progress, prevents data loss, and ensures UI consistency.
>
> **Implementation summary:**
> - When `updateTherapeuticProperties` is called, it checks each incoming property against the current state.
> - If the current property is enriched, it is preserved; otherwise, the new property is used (with enrichment fields normalized).
> - This logic applies to all sources, including streaming, enrichment, and error recovery.
>
> **Example logic:**
> ```typescript
> const mergedProperties = properties.map(newProp => {
>   const existing = currentState.find(cp => cp.property_id === newProp.property_id);
>   if (existing && existing.isEnriched && existing.suggested_oils && existing.suggested_oils.length > 0) {
>     return existing;
>   }
>   return {
>     ...newProp,
>     isEnriched: newProp.isEnriched ?? false,
>     suggested_oils: newProp.suggested_oils?.map(oil => ({
>       ...oil,
>       isEnriched: oil.isEnriched ?? false
>     }))
>   };
> });
> ```
>
> **Result:**
> - No more state loss after enrichment.
> - Batch updates are safe and idempotent.
> - All enrichment progress is preserved across the entire property lifecycle.

#### E. Performance Optimizations
1. **Batch Processing**
   - Single OpenAI API call for all embeddings
   - Bulk vector search in Supabase
   - Parallel streaming for multiple properties

2. **State Management**
   - Properties maintain individual loading states
   - Enrichment status tracked per property
   - ⚠️ **Critical:** Prevent multiple enrichment triggers with triple-check
     ```typescript
     // Required checks in handleStreamingResults
     if (suggested_oils.length > 0 && 
         !p.isEnriched && 
         propertyEnrichmentStatus[p.property_id] !== 'loading')
     ```
     This prevents:
     - Multiple enrichment calls during re-renders
     - Concurrent enrichment requests for the same property
     - Race conditions between enrichment requests

3. **Resource Cleanup**
   - Optimistic updates for UI responsiveness
   - Efficient re-rendering with selective updates
   - Proper cleanup of streaming resources

4. **Error Recovery**
   - Automatic retries for failed enrichments
   - Fallback to basic data on enrichment failure
   - Preservation of partial enrichments

### 4. Key Files and Their Roles

1. **Type Definitions** (`src/features/create-recipe/types/recipe.types.ts`)
   - Defines all interfaces and types
   - Includes request/response types
   - Defines enrichment interfaces

2. **Schema Validation** (`src/features/create-recipe/schemas/recipe-schemas.ts`)
   - Zod schemas for validation
   - Request/response validation
   - Type inference for TypeScript

3. **API Integration**
   - `src/lib/ai/tools/supabase-oil-search-tool.ts`: Vector search
   - `src/lib/ai/tools/suggested-oils-search-tool.ts`: Contextual search
   - `src/app/api/ai/streaming/route.ts`: Streaming endpoint

4. **Frontend Components**
   - `properties-display.tsx`: Main display component
   - `therapeutic-properties-table.tsx`: Data presentation
   - Loading and error components

### 5. Error Handling and Edge Cases

1. **Enrichment Failures**
   - Retries for failed enrichments
   - Fallback to basic oil information
   - Error state management

2. **Loading States**
   - Per-property loading indicators
   - Batch enrichment progress tracking
   - Error state visualization

3. **Data Validation**
   - Schema validation at each step
   - Type checking throughout flow
   - Error boundaries for component safety

### 6. Performance Considerations

1. **Parallel Processing**
   - Multiple properties stream and process simultaneously
   - Independent property lifecycles prevent blocking
   - Efficient resource utilization through parallel execution

2. **Data Structure Optimizations**
   - Results pre-grouped by embedding index
   - Minimal object creation and copying
   - Efficient state updates with Map data structures

3. **UI Optimizations**
   - Progressive loading with property-scoped updates
   - Optimistic UI updates for better responsiveness
   - Selective re-rendering based on property changes
   - Proper cleanup of streaming resources

4. **Preventing Multiple Enrichment Triggers**
   - Properties maintain individual loading states
   - Enrichment status tracked per property
   - ⚠️ **Critical:** Prevent multiple enrichment triggers
     ```typescript
     // Required check in handleStreamingResults
     if (parallelStreamingState.results.has(p.property_id) && !p.isEnriched)
     ```
     Without this check, each property could trigger 3-4 unnecessary enrichment calls during normal operation due to React's re-render cycles.

### Related Files

1. **Core Logic**
   - `src/features/create-recipe/types/recipe.types.ts`
   - `src/features/create-recipe/schemas/recipe-schemas.ts`
   - `src/features/create-recipe/utils/api-data-transform.ts`

2. **Services**
   - `src/lib/ai/services/batch-enrichment.service.ts`
   - `src/lib/ai/services/unified-embeddings.service.ts`

3. **Components**
   - `src/features/create-recipe/components/properties-display.tsx`
   - `src/features/create-recipe/components/therapeutic-properties-table.tsx`
   - `src\components\ui\safety-details-tabs.tsx`
   - `src\components\ui\safety-status-badge.tsx`

4. **Hooks**
   - `src/features/create-recipe/hooks/use-create-recipe-streaming.ts`
   - `src/lib/ai/hooks/use-parallel-streaming-engine.ts`

### 7. Type Safety and Database Fields

The enrichment process relies on strict type definitions across multiple files to ensure data consistency and type safety. When the safety database returns data, all fields must be properly typed in the following files:

1. **Core Type Definitions** (`src/features/create-recipe/types/recipe.types.ts`)
   ```typescript
   // Base oil interface (AI suggestion)
   export interface EssentialOil {
     oil_id: string;
     name_english: string;
     name_botanical: string;  // Used only for search
     name_localized: string;
     match_rationale_localized: string;
     relevancy_to_property_score: number;
   }

   // Enriched oil with safety data
   export interface EnrichedEssentialOil extends EssentialOil {
     supabase_id?: string;
     name_scientific?: string;  // From safety database
     description?: string;
     safety?: OilSafetyInfo;
     isEnriched: boolean;
     botanical_mismatch?: boolean;
     similarity_score?: number;
   }

   // Safety information structure
   export interface OilSafetyInfo {
     internal_use?: {
       name?: string | null;
       code?: string | null;
       description?: string | null;
       guidance?: string | null;
     };
     dilution?: {
       name?: string | null;
       description?: string | null;
       percentage_max?: number | null;
       percentage_min?: number | null;
       ratio?: string | null;
     };
     phototoxicity?: {
       status?: string | null;
       guidance?: string | null;
       description?: string | null;
     };
     pregnancy_nursing?: Array<{
       id?: string | null;
       name?: string | null;
       status_description?: string | null;
       code?: string | null;
       usage_guidance?: string | null;
       description?: string | null;
     }>;
     child_safety?: Array<{
       age_range?: string | null;
       safety_notes?: string | null;
     }>;
   }
   ```

2. **Enrichment Service** (`src/lib/ai/services/batch-enrichment.service.ts`)
   ```typescript
   export interface SuggestedOilData {
     oil_id: string;
     name_english: string;
     name_botanical: string;
     name_localized: string;
     match_rationale_localized: string;
     relevancy_to_property_score: number;
     isEnriched?: boolean;
   }

   export interface EnrichedOilData extends SuggestedOilData {
     name_scientific?: string;  // Must match safety database field
     safety?: OilSafetyData;
     enrichment_status: 'enriched' | 'not_found' | 'discarded';
     botanical_mismatch?: boolean;
     similarity_score?: number;
     search_query?: string;
     enrichment_timestamp?: string;
     isEnriched: boolean;
   }
   ```

3. **Frontend Components**
   - `src/features/create-recipe/components/therapeutic-properties-table.tsx`
   - `src/components/ui/safety-details-tabs.tsx`
   - `src/components/ui/safety-status-badge.tsx`

   These components rely on the type definitions and will show TypeScript errors if fields are missing or incorrectly typed.

#### Critical Type Dependencies

1. **Safety Database Fields → Type Definitions**
   - Every field returned by the safety database must be represented in `OilSafetyInfo`
   - The `name_scientific` field must be included in `EnrichedEssentialOil`
   - All safety status fields must match the expected structure

2. **Type Definitions → Components**
   - Components use type definitions to access fields safely
   - TypeScript will error if trying to access undefined fields
   - Components must check field existence before rendering

3. **Service → Type Definitions**
   - Enrichment service must map database results to match type definitions
   - Any new safety database fields must be added to both service and type definitions

#### Adding New Safety Database Fields

When the safety database schema changes:

1. Update `OilSafetyInfo` in `recipe.types.ts`
2. Update `EnrichedOilData` in `batch-enrichment.service.ts`
3. Update mapping in `processSearchResults` function
4. Update any components that will display the new fields

Example of proper field mapping:
```typescript
const enrichedOil: EnrichedOilData = {
  ...oil,
  name_scientific: bestMatch.name_scientific,  // Must match database field name
  safety: {
    internal_use: bestMatch.internal_use,
    dilution: bestMatch.dilution,
    phototoxicity: bestMatch.phototoxicity,
    pregnancy_nursing: bestMatch.pregnancy_nursing_safety,
    child_safety: bestMatch.child_safety
  },
  enrichment_status: 'enriched',
  botanical_mismatch: botanicalMismatch,
  similarity_score: similarity,
  isEnriched: true
};
```

This strict typing ensures:
- Type safety across the entire application
- Clear documentation of required fields
- Early detection of missing or incorrect fields
- Consistent data structure throughout the enrichment process