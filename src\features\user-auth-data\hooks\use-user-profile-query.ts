'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getUserProfile } from '../queries/profile.queries';
import * as Sentry from '@sentry/nextjs';
import { useEffect, useRef, useCallback } from 'react';
import { type UserProfile } from '../schemas/profile.schema';

interface UseUserProfileQueryOptions {
  enabled?: boolean;
  retry?: boolean | number | ((failureCount: number, error: any) => boolean);
  retryDelay?: number | ((attemptIndex: number) => number);
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  // Enhanced options
  backgroundSync?: boolean;
  optimisticUpdates?: boolean;
  prefetchRelated?: boolean;
}

interface QueryPerformanceMetrics {
  fetchCount: number;
  lastFetchTime: number;
  averageFetchTime: number;
  totalFetchTime: number;
  cacheHitCount: number;
  backgroundSyncCount: number;
}

// Global performance tracking for profile queries
const performanceTracker = new Map<string, QueryPerformanceMetrics>();

/**
 * Hook for fetching user profile data with advanced optimizations:
 * - Enhanced error recovery with exponential backoff
 * - Performance monitoring and metrics
 * - Background sync capabilities
 * - Connection resilience patterns
 * - Cache invalidation strategies
 * - Optimistic updates support
 */
export function useUserProfileQuery(userId?: string, options: UseUserProfileQueryOptions = {}) {
  const debugAuth = process.env.NODE_ENV === 'development' && 
                    process.env['NEXT_PUBLIC_DEBUG_AUTH'] === 'true';
  
  const queryClient = useQueryClient();
  
  // Enhanced default options
  const {
    enabled = true,
    retry = 3,
    retryDelay = (attemptIndex) => Math.min(1000 * Math.pow(2, attemptIndex), 10000),
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus = false,
    refetchOnReconnect = true,
    backgroundSync = true,
    optimisticUpdates = false,
    prefetchRelated = false,
    ...restOptions
  } = options;
  
  // Track previous significant states to reduce logging noise
  const previousSignificantStateRef = useRef<string>('');
  
  // Performance tracking
  const performanceRef = useRef<QueryPerformanceMetrics>(() => 
    performanceTracker.get(userId || 'anonymous') || {
      fetchCount: 0,
      lastFetchTime: 0,
      averageFetchTime: 0,
      totalFetchTime: 0,
      cacheHitCount: 0,
      backgroundSyncCount: 0
    }
  );
  
  // Connection state tracking
  const isOnlineRef = useRef(typeof navigator !== 'undefined' && navigator.onLine);
  
  // Enhanced query with performance tracking
  const queryResult = useQuery({
    queryKey: userId ? ['userProfile', userId] : ['userProfile'],
    queryFn: async () => {
      const startTime = Date.now();
      
      try {
        const result = await getUserProfile(userId!);
        
        // Update performance metrics
        const endTime = Date.now();
        const fetchTime = endTime - startTime;
        
        if (userId) {
          const metrics = performanceRef.current;
          metrics.fetchCount++;
          metrics.lastFetchTime = fetchTime;
          metrics.totalFetchTime += fetchTime;
          metrics.averageFetchTime = metrics.totalFetchTime / metrics.fetchCount;
          
          performanceTracker.set(userId, metrics);
        }
        
        if (debugAuth && userId) {
          console.log(`[useUserProfileQuery] Fetch completed in ${fetchTime}ms for ${userId.substring(0, 6)}...`);
        }
        
        return result;
      } catch (error) {
        // Enhanced error context for debugging
        if (debugAuth && userId) {
          console.error(`[useUserProfileQuery] Fetch failed for ${userId.substring(0, 6)}...`, error);
        }
        throw error;
      }
    },
    enabled: !!userId && enabled,
    retry: (failureCount, error) => {
      // Enhanced retry logic with connection awareness
      if (!isOnlineRef.current) {
        if (debugAuth) {
          console.log(`[useUserProfileQuery] Skipping retry due to offline status`);
        }
        return false;
      }
      
      if (typeof retry === 'function') {
        return retry(failureCount, error);
      }
      
      return typeof retry === 'number' ? failureCount < retry : retry;
    },
    retryDelay,
    staleTime,
    gcTime: cacheTime,
    refetchOnWindowFocus,
    refetchOnReconnect,
    // Enhanced options
    networkMode: 'offlineFirst', // Better offline support
    ...restOptions
  });
  
  const { error, isError, data, isFetching, isStale } = queryResult;
  
  // Background sync functionality
  useEffect(() => {
    if (!backgroundSync || !userId || !data || isError) return;
    
    const syncInterval = setInterval(() => {
      if (isOnlineRef.current && !isFetching) {
        queryClient.invalidateQueries({
          queryKey: ['userProfile', userId],
          exact: true
        });
        
        if (userId) {
          const metrics = performanceRef.current;
          metrics.backgroundSyncCount++;
          performanceTracker.set(userId, metrics);
        }
        
        if (debugAuth) {
          console.log(`[useUserProfileQuery] Background sync triggered for ${userId.substring(0, 6)}...`);
        }
      }
    }, 5 * 60 * 1000); // Every 5 minutes
    
    return () => clearInterval(syncInterval);
  }, [backgroundSync, userId, data, isError, isFetching, queryClient, debugAuth]);
  
  // Online/offline status tracking
  useEffect(() => {
    const handleOnline = () => {
      isOnlineRef.current = true;
      if (userId && isError) {
        // Retry failed queries when coming back online
        queryClient.invalidateQueries({
          queryKey: ['userProfile', userId],
          exact: true
        });
      }
    };
    
    const handleOffline = () => {
      isOnlineRef.current = false;
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, [userId, isError, queryClient]);
  
  // Prefetch related data if enabled
  useEffect(() => {
    if (prefetchRelated && data && userId) {
      // Prefetch related user data that might be needed
      queryClient.prefetchQuery({
        queryKey: ['userPreferences', userId],
        queryFn: () => {
          // This would be implemented based on your needs
          return Promise.resolve(null);
        },
        staleTime: 5 * 60 * 1000,
      });
    }
  }, [prefetchRelated, data, userId, queryClient]);
  
  // Log only significant query state changes in debug mode
  useEffect(() => {
    if (debugAuth && userId) {
      const maskedUserId = `${userId.substring(0, 6)}...`;
      
      // Only log significant state changes to reduce noise
      const significantState = `${queryResult.status}-${queryResult.isLoading}-${queryResult.isError}-${isStale}`;
      
      if (significantState !== previousSignificantStateRef.current) {
        const metrics = performanceRef.current;
        
        console.log(`[useUserProfileQuery] State change for userId ${maskedUserId}:`, {
          status: queryResult.status,
          isLoading: queryResult.isLoading,
          isSuccess: queryResult.isSuccess,
          isError: queryResult.isError,
          isStale,
          isFetching: queryResult.isFetching,
          hasData: !!queryResult.data,
          // Performance metrics
          performance: {
            fetchCount: metrics.fetchCount,
            averageFetchTime: Math.round(metrics.averageFetchTime),
            cacheHitCount: metrics.cacheHitCount,
            backgroundSyncCount: metrics.backgroundSyncCount
          }
        });
        previousSignificantStateRef.current = significantState;
      }
    }
  }, [
    queryResult.status, 
    queryResult.isLoading, 
    queryResult.isError, 
    isStale,
    queryResult.isFetching,
    userId, 
    debugAuth
  ]);
  
  // Report significant errors to Sentry with enhanced context
  useEffect(() => {
    if (isError && error && userId) {
      // Mask userId for privacy
      const maskedUserId = `${userId.substring(0, 6)}...`;
      const metrics = performanceRef.current;
      
      Sentry.captureException(error, {
        tags: { 
          component: 'useUserProfileQuery', 
          type: 'profileError',
          isOnline: isOnlineRef.current
        },
        extra: { 
          userId: maskedUserId,
          operation: 'useUserProfileQuery',
          message: "Error fetching user profile",
          performance: {
            fetchCount: metrics.fetchCount,
            averageFetchTime: metrics.averageFetchTime,
            lastFetchTime: metrics.lastFetchTime
          },
          connectionState: {
            isOnline: isOnlineRef.current,
            wasStale: isStale
          }
        }
      });
    }
  }, [isError, error, userId, isStale]);
  
  // Enhanced return with additional utilities
  const enhancedResult = {
    ...queryResult,
    // Performance metrics (only in debug mode)
    _performance: debugAuth && userId ? performanceRef.current : undefined,
    // Cache utilities
    invalidateCache: useCallback(() => {
      if (userId) {
        queryClient.invalidateQueries({
          queryKey: ['userProfile', userId],
          exact: true
        });
      }
    }, [userId, queryClient]),
    // Prefetch utility
    prefetch: useCallback(() => {
      if (userId) {
        return queryClient.prefetchQuery({
          queryKey: ['userProfile', userId],
          queryFn: () => getUserProfile(userId),
          staleTime,
        });
      }
    }, [userId, queryClient, staleTime]),
    // Connection state
    isOnline: isOnlineRef.current,
  };
  
  return enhancedResult;
}
