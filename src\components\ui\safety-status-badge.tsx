import * as React from 'react';
import { cn } from '@/lib/utils';
import { Shield, Droplets, Sun, Baby, AlertTriangle, CheckCircle } from 'lucide-react';

interface SafetyStatusBadgeProps {
  /** The safety category */
  category: 'internal' | 'dilution' | 'phototoxicity' | 'pregnancy' | 'child';
  /** The safety data object */
  safety?: any;
  /** Display variant */
  variant?: 'default' | 'compact';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Compact safety status badges for oil safety information
 * Used in the nested table design to show safety status at a glance
 */
export function SafetyStatusBadge({ 
  category, 
  safety, 
  variant = 'default',
  className 
}: SafetyStatusBadgeProps) {
  if (!safety) return null;

  const getSafetyInfo = () => {
    switch (category) {
      case 'internal':
        return {
          icon: Shield,
          status: safety.name || 'Unknown',
          colorScheme: getInternalUseColor(safety.name),
          title: `Internal Use: ${safety.name || 'Unknown'}`
        };
      case 'dilution':
        return {
          icon: Droplets,
          status: safety.name || 'Unknown',
          colorScheme: getDilutionColor(safety.name),
          title: `Dilution: ${safety.name || 'Unknown'}`
        };
      case 'phototoxicity':
        return {
          icon: Sun,
          status: safety.status || 'Unknown',
          colorScheme: getPhototoxicityColor(safety.status),
          title: `Phototoxicity: ${safety.status || 'Unknown'}`
        };
      case 'pregnancy':
        return {
          icon: Baby,
          status: safety.length > 0 ? safety[0].name || 'Check Details' : 'Unknown',
          colorScheme: getPregnancyColor(safety),
          title: `Pregnancy Safety: ${safety.length > 0 ? safety[0].name || 'Check Details' : 'Unknown'}`
        };
      case 'child':
        return {
          icon: Baby,
          status: safety.length > 0 ? safety[0].age_range || 'Check Details' : 'Unknown',
          colorScheme: getChildSafetyColor(safety),
          title: `Child Safety: ${safety.length > 0 ? safety[0].age_range || 'Check Details' : 'Unknown'}`
        };
      default:
        return {
          icon: AlertTriangle,
          status: 'Unknown',
          colorScheme: 'gray',
          title: 'Unknown Safety Category'
        };
    }
  };

  const { icon: Icon, status, colorScheme, title } = getSafetyInfo();

  if (variant === 'compact') {
    return (
      <span 
        className={cn(
          'inline-flex items-center justify-center w-6 h-6 rounded-md text-xs font-medium border transition-colors',
          colorScheme === 'green' && 'bg-green-100 text-green-800 border-green-200',
          colorScheme === 'blue' && 'bg-blue-100 text-blue-800 border-blue-200',
          colorScheme === 'amber' && 'bg-amber-100 text-amber-800 border-amber-200',
          colorScheme === 'red' && 'bg-red-100 text-red-800 border-red-200',
          colorScheme === 'purple' && 'bg-purple-100 text-purple-800 border-purple-200',
          colorScheme === 'gray' && 'bg-gray-100 text-gray-800 border-gray-200',
          className
        )}
        title={title}
      >
        <Icon className="h-3 w-3" />
      </span>
    );
  }

  return (
    <span 
      className={cn(
        'inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border transition-colors',
        colorScheme === 'green' && 'bg-green-100 text-green-800 border-green-200',
        colorScheme === 'blue' && 'bg-blue-100 text-blue-800 border-blue-200',
        colorScheme === 'amber' && 'bg-amber-100 text-amber-800 border-amber-200',
        colorScheme === 'red' && 'bg-red-100 text-red-800 border-red-200',
        colorScheme === 'purple' && 'bg-purple-100 text-purple-800 border-purple-200',
        colorScheme === 'gray' && 'bg-gray-100 text-gray-800 border-gray-200',
        className
      )}
      title={title}
    >
      <Icon className="h-3 w-3" />
      <span className="truncate max-w-20">{status}</span>
    </span>
  );
}

// Helper functions for color coding
function getInternalUseColor(status?: string): string {
  if (!status) return 'gray';
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('safe') || lowerStatus.includes('approved')) return 'green';
  if (lowerStatus.includes('avoid') || lowerStatus.includes('not recommended')) return 'red';
  return 'blue';
}

function getDilutionColor(status?: string): string {
  if (!status) return 'gray';
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('neat') || lowerStatus.includes('[n]')) return 'green';
  if (lowerStatus.includes('high') || lowerStatus.includes('strong')) return 'amber';
  return 'blue';
}

function getPhototoxicityColor(status?: string): string {
  if (!status) return 'gray';
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('non-phototoxic') || lowerStatus.includes('safe')) return 'green';
  if (lowerStatus.includes('phototoxic') || lowerStatus.includes('caution')) return 'amber';
  return 'blue';
}

function getPregnancyColor(safety: any[]): string {
  if (!safety || safety.length === 0) return 'gray';
  const status = safety[0]?.name?.toLowerCase() || '';
  if (status.includes('safe') || status.includes('approved')) return 'green';
  if (status.includes('avoid') || status.includes('not recommended')) return 'red';
  return 'amber';
}

function getChildSafetyColor(safety: any[]): string {
  if (!safety || safety.length === 0) return 'gray';
  const status = safety[0]?.safety_notes?.toLowerCase() || '';
  if (status.includes('safe') || status.includes('appropriate')) return 'green';
  if (status.includes('avoid') || status.includes('not recommended')) return 'red';
  return 'amber';
} 