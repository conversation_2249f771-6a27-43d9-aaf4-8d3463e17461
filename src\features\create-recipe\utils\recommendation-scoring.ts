/**
 * @fileoverview Recommendation Scoring Utilities
 * 
 * This module provides utilities for the essential oil recommendation scoring system.
 * It implements a data processing pipeline that:
 * 1. Enriches normalized data with calculated scores
 * 2. Aggregates scores to create final ranked recommendations
 * 
 * The scoring system adds two key fields:
 * - property_coverage_score: How relevant a property is to the user's problem
 * - recommendation_instance_score: Score for a specific oil within a property context
 */

import type { TherapeuticProperty, EssentialOil } from '../types/recipe.types';

/**
 * Maximum relevancy score used for normalization
 */
const MAX_RELEVANCY_SCORE = 5;

/**
 * Interface for enriched therapeutic property (with calculated scores)
 */
export interface EnrichedTherapeuticProperty extends Omit<TherapeuticProperty, 'suggested_oils'> {
  property_coverage_score: number;
  suggested_oils: EnrichedOilSuggestion[];
  isEnriched: boolean; // Required field from TherapeuticProperty
}

/**
 * Interface for enriched oil suggestion (with calculated scores)
 */
export interface EnrichedOilSuggestion {
  oil_id: string;
  match_rationale_localized: string;
  relevancy_to_property_score: number;
  recommendation_instance_score: number;
}

/**
 * Interface for final ranked oil recommendation
 */
export interface FinalRankedOil {
  oil_id: string;
  name_localized: string;
  final_relevance_score: number;
}

/**
 * Interface for enriched recommendation data
 */
export interface EnrichedRecommendationData {
  therapeuticProperties: EnrichedTherapeuticProperty[];
  oils: EssentialOil[];
}

/**
 * Calculate property coverage score
 * 
 * This normalizes the relevancy_score to a 0-1 scale based on the maximum possible score.
 * A higher score indicates the property is more relevant to the user's specific problem.
 * 
 * @param property - The therapeutic property
 * @returns Normalized coverage score (0-1)
 */
export function calculatePropertyCoverageScore(property: TherapeuticProperty): number {
  return property.relevancy_score / MAX_RELEVANCY_SCORE;
}

/**
 * Calculate recommendation instance score
 * 
 * This calculates the score for a specific oil recommendation within a property's context.
 * Formula: relevancy_to_property_score × relevancy_score × property_coverage_score
 * 
 * @param oilSuggestion - The oil suggestion
 * @param property - The therapeutic property containing this suggestion
 * @returns The calculated instance score
 */
export function calculateRecommendationInstanceScore(
  oilSuggestion: any,
  property: TherapeuticProperty
): number {
  const propertyCoverageScore = calculatePropertyCoverageScore(property);
  return oilSuggestion.relevancy_to_property_score * property.relevancy_score * propertyCoverageScore;
}

/**
 * Enrich recommendation data with calculated scores
 * 
 * This function takes normalized recommendation data and enriches it by adding:
 * - property_coverage_score to each therapeutic property
 * - recommendation_instance_score to each oil suggestion
 * 
 * @param recommendationData - The normalized recommendation data
 * @returns Enriched data with calculated scores
 */
export function enrichRecommendationDataWithScores(recommendationData: {
  therapeuticProperties: TherapeuticProperty[];
  oils: EssentialOil[];
}): EnrichedRecommendationData {
  const enrichedProperties: EnrichedTherapeuticProperty[] = recommendationData.therapeuticProperties.map(property => {
    // Calculate property coverage score
    const propertyCoverageScore = calculatePropertyCoverageScore(property);
    
    // Enrich oil suggestions with instance scores
    const enrichedOilSuggestions: EnrichedOilSuggestion[] = (property.suggested_oils || [])
      .filter(oil => oil.enrichment_status === 'enriched') // Only include enriched oils
      .map(oilSuggestion => ({
        oil_id: oilSuggestion.oil_id,
        match_rationale_localized: oilSuggestion.match_rationale_localized,
        relevancy_to_property_score: oilSuggestion.relevancy_to_property_score,
        recommendation_instance_score: calculateRecommendationInstanceScore(oilSuggestion, property)
      }));

    return {
      property_id: property.property_id,
      property_name_localized: property.property_name_localized,
      property_name_english: property.property_name_english,
      description_contextual_localized: property.description_contextual_localized,
      addresses_cause_ids: property.addresses_cause_ids,
      addresses_symptom_ids: property.addresses_symptom_ids,
      relevancy_score: property.relevancy_score,
      isEnriched: property.isEnriched, // Preserve the original isEnriched field
      property_coverage_score: propertyCoverageScore,
      suggested_oils: enrichedOilSuggestions
    };
  });

  return {
    therapeuticProperties: enrichedProperties,
    oils: recommendationData.oils
  };
}

/**
 * Calculate final ranked list of oil recommendations
 * 
 * This function aggregates all recommendation_instance_score values for each unique oil
 * and returns a sorted list of final recommendations.
 * 
 * @param enrichedRecommendationData - The enriched recommendation data
 * @returns Array of oils ranked by final relevance score
 */
export function calculateFinalRankedList(enrichedRecommendationData: EnrichedRecommendationData): FinalRankedOil[] {
  const oilFinalScores: Record<string, number> = {};
  
  // Sum up all instance scores for each oil
  enrichedRecommendationData.therapeuticProperties.forEach(property => {
    property.suggested_oils.forEach(oilSuggestion => {
      const oilId = oilSuggestion.oil_id;
      const instanceScore = oilSuggestion.recommendation_instance_score;
      
      if (!oilFinalScores[oilId]) {
        oilFinalScores[oilId] = 0;
      }
      oilFinalScores[oilId] += instanceScore;
    });
  });
  
  // Create final ranked list
  const finalRankedList: FinalRankedOil[] = Object.entries(oilFinalScores).map(([oilId, finalScore]) => {
    // Find the oil in the oils array to get its localized name
    const oilData = enrichedRecommendationData.oils.find(oil => oil.oil_id === oilId);
    
    return {
      oil_id: oilId,
      name_localized: oilData?.name_localized || oilData?.name_english || 'Unknown Oil',
      final_relevance_score: Math.round(finalScore * 100) / 100 // Round to 2 decimal places
    };
  });
  
  // Sort by final relevance score (highest first)
  return finalRankedList.sort((a, b) => b.final_relevance_score - a.final_relevance_score);
}

/**
 * Utility function to get oil name from oils array
 * 
 * @param oilId - The oil ID to look up
 * @param oils - Array of oils to search in
 * @returns The localized name of the oil
 */
export function getOilName(oilId: string, oils: EssentialOil[]): string {
  const oil = oils.find(oil => oil.oil_id === oilId);
  return oil?.name_localized || oil?.name_english || 'Unknown Oil';
} 