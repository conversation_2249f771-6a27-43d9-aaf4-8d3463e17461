---
trigger: always_on
---

**"Make edits incrementally. Split changes into small, focused steps—one logical change per step. Ensure each step is independently testable and debuggable. Do not combine multiple unrelated edits in a single change."**

### Key Rules:
1. **Single Responsibility** – Each edit addresses only one task/fix.
2. **Testable Units** – Changes must not break functionality between steps.
3. **Explicit Order** – Specify sequence if dependencies exist (e.g., "First X, then Y").