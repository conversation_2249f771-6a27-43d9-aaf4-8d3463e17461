{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "typecheck": "tsc --noEmit", "test:streaming": "node scripts/test-streaming.js", "demo:streaming": "node scripts/demo-streaming.js", "validate:streaming": "node scripts/validate-streaming.js", "demo:typing": "node scripts/test-structured-typing.js", "test:oils": "node scripts/test-oils-integration.js", "test:pinecone": "node scripts/test-pinecone-search.js", "performance:baseline": "node scripts/monitor-performance-baseline.js", "performance:navigation": "node scripts/verify-navigation-timing.js", "performance:webpack": "node scripts/validate-webpack-optimizations.js", "pubmed:cli": "npx tsx own-openai-examples/pubmed-research-agent/index.ts", "pubmed:orchestrated": "npx tsx own-openai-examples/pubmed-research-agent/fixed-cli.ts"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@modelcontextprotocol/sdk": "^1.12.3", "@openai/agents": "^0.0.4", "@pinecone-database/pinecone": "^6.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/nextjs": "^9.24.0", "@supabase/ssr": "^0.4.0", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-table": "^8.21.3", "best-effort-json-parser": "^1.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.8.1", "framer-motion": "^11.4.0", "fs": "^0.0.1-security", "js-yaml": "^4.1.0", "limiter": "^3.0.0", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-themes": "^0.4.6", "patch-package": "^8.0.0", "posthog-js": "^1.145.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsx": "^4.20.3", "winston": "^3.13.1", "zod": "^3.24.2", "zustand": "^4.5.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "chalk": "^5.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jsdom": "^26.1.0", "ora": "^8.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "typescript": "^5"}}