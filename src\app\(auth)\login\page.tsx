
import { Suspense } from 'react';
import { LoginForm } from '@/features/auth/components';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

/**
 * Loading fallback component for the login form
 */
function LoginLoading() {
  return (
    <div className="w-full animate-fade-in">
      <Card className="w-full shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Welcome back</CardTitle>
          <CardDescription>Loading login form...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Renders the login page for the PassForge application.
 * This page allows users to sign in using their email and password.
 * It primarily displays the `LoginForm` component which handles the form submission and authentication logic.
 * Uses Suspense boundary to handle dynamic useSearchParams() usage in LoginForm.
 *
 * @returns {JSX.Element} The login page component.
 */
export default function LoginPage(): JSX.Element {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginForm />
    </Suspense>
  );
}
