# Create Recipe Components

This directory contains all the components used in the create recipe feature.

## 📁 Directory Overview

### Core Recipe Components
- `recipe-stepper.tsx` - Main recipe creation wizard stepper
- `therapeutic-properties-table.tsx` - **[NEW UI]** Properties table with oil enrichment
- `causes-selection-table.tsx` - Causes selection interface
- `symptoms-selection-table.tsx` - Symptoms selection interface

### Oil Enrichment UI Components (New)
The oil enrichment feature has been completely redesigned. See the comprehensive documentation:

👉 **[Oil Enrichment UI Documentation](./oil-enrichment-ui-documentation.md)**

**Related Files:**
- `src/components/ui/safety-status-badge.tsx` - Compact safety badges
- `src/components/ui/safety-details-tabs.tsx` - Expandable safety details
- `therapeutic-properties-table.tsx` - Main implementation

### Navigation Components
- `recipe-navigation-buttons.tsx` - Reusable navigation buttons
- `recipe-form-navigation-buttons.tsx` - Form-specific navigation
- `recipe-streaming-navigation-buttons.tsx` - Streaming state navigation

### Utility Components
- `relevancy-badge.tsx` - Shows match scores for oils/properties
- `ai-streaming-modal.tsx` - Modal for AI streaming states
- `recipe-progress-indicator.tsx` - Progress tracking

## 🚀 Quick Start

### For Oil Enrichment Changes
1. Read the [Oil Enrichment UI Documentation](./oil-enrichment-ui-documentation.md)
2. Check the component architecture diagram
3. Review the troubleshooting section for common issues

### For General Recipe Components
1. Check existing components for patterns
2. Follow the navigation component structure
3. Use TypeScript interfaces from `../types/recipe.types.ts`

## 📋 Recent Changes

### Oil Enrichment UI Redesign (v1.0)
- ✅ **70% space reduction** from bulky cards to compact table
- ✅ **Progressive disclosure** with expandable safety details
- ✅ **Consistent design** matching app's table patterns
- ✅ **Mobile responsive** design
- ✅ **Accessibility improvements**

### Navigation Standardization
- ✅ **Unified navigation components** across all recipe flows
- ✅ **Eliminated 150+ lines** of duplicate code
- ✅ **Consistent styling** and behavior

## 🔗 Related Documentation

- [Oil Enrichment UI Documentation](./oil-enrichment-ui-documentation.md) - Complete redesign guide
- [Schema Change Guide](../readme/schema-change-guide.md) - How to update AI schemas
- [Development Guide](../readme/development-guide.md) - General development practices
- [AI Streaming Architecture](../readme/ai-streaming-architecture.md) - Streaming implementation

## 🤝 Contributing

1. **Follow existing patterns** - Check similar components first
2. **Update documentation** - Keep README and documentation current
3. **Test thoroughly** - Use the testing guide in oil enrichment docs
4. **Maintain consistency** - Follow ShadCN design system

---

*Last updated: December 30, 2024* 