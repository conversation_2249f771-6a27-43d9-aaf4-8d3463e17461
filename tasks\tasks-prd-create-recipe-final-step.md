# Task List: Create Recipe Final Step Implementation

## Relevant Files

- `src/features/create-recipe/types/recipe.types.ts` - Add new types for final step data structures and recipe protocols
- `src/features/create-recipe/types/recipe.types.test.ts` - Unit tests for new type definitions
- `src/features/create-recipe/constants/recipe.constants.ts` - Update step definitions to replace OILS step with FINAL_RECIPES step
- `src/features/create-recipe/store/recipe-store.ts` - Extend store with final step state management and recipe data
- `src/features/create-recipe/store/recipe-store.test.ts` - Unit tests for store extensions
- `src/features/create-recipe/prompts/final-recipes.yaml` - AI prompt template for recipe generation based on synergistic-oils.yaml
- `src/features/create-recipe/components/final-recipes-display.tsx` - Main component for final step displaying three recipe protocols
- `src/features/create-recipe/components/final-recipes-display.test.tsx` - Unit tests for main final recipes component
- `src/features/create-recipe/components/recipe-protocol-card.tsx` - Individual recipe card component (morning/mid-day/night)
- `src/features/create-recipe/components/recipe-protocol-card.test.tsx` - Unit tests for recipe card component
- `src/features/create-recipe/components/container-recommendation.tsx` - Component for container and dispensing system recommendations
- `src/features/create-recipe/components/container-recommendation.test.tsx` - Unit tests for container recommendation component
- `src/features/create-recipe/components/safety-warnings.tsx` - Component for displaying safety warnings and rationale
- `src/features/create-recipe/components/safety-warnings.test.tsx` - Unit tests for safety warnings component
- `src/features/create-recipe/hooks/use-final-recipes-generation.ts` - Hook for managing parallel AI recipe generation
- `src/features/create-recipe/hooks/use-final-recipes-generation.test.ts` - Unit tests for recipe generation hook
- `src/features/create-recipe/utils/recipe-data-transformer.ts` - Utility for transforming wizard data to AI-ready format
- `src/features/create-recipe/utils/recipe-data-transformer.test.ts` - Unit tests for data transformation utilities
- `src/features/create-recipe/utils/safety-filter.ts` - Utility for filtering dermocaustic oils based on demographics
- `src/features/create-recipe/utils/safety-filter.test.ts` - Unit tests for safety filtering logic
- `src/features/create-recipe/schemas/final-recipes-schemas.ts` - Zod schemas for validating AI responses and recipe data
- `src/features/create-recipe/schemas/final-recipes-schemas.test.ts` - Unit tests for schema validation
- `src/features/create-recipe/components/wizard-container.tsx` - Update to include final recipes step in step renderer
- `src/features/create-recipe/hooks/use-recipe-navigation.ts` - Update navigation logic to handle final step restrictions
- `src/locales/en/create-recipe.json` - English translations for final step UI text
- `src/locales/es/create-recipe.json` - Spanish translations for final step UI text  
- `src/locales/pt/create-recipe.json` - Portuguese translations for final step UI text

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Follow existing patterns in `src/features/create-recipe` for consistency
- Ensure all components stay under 500 lines following DRY and KISS principles
- Mock data should match `docs/create-recipe/new-final-step/json-dataset-minimal.json` structure

## Tasks

- [ ] 1.0 Update Core Architecture and Types
  - [ ] 1.1 Add FINAL_RECIPES enum value to RecipeStep in `src/features/create-recipe/types/recipe.types.ts`
  - [ ] 1.2 Create FinalRecipeProtocol, RecipeTimeSlot, ContainerRecommendation, and SafetyWarning interfaces
  - [ ] 1.3 Add FinalRecipesState interface with properties for generated recipes, loading states, and error handling
  - [ ] 1.4 Update WIZARD_STEPS constant in `src/features/create-recipe/constants/recipe.constants.ts` to replace OILS with FINAL_RECIPES
  - [ ] 1.5 Update step configuration with proper path, title, and navigation properties for final step
  - [ ] 1.6 Write comprehensive unit tests for all new type definitions and constants

- [ ] 2.0 Implement AI Recipe Generation System
  - [ ] 2.1 Create `src/features/create-recipe/prompts/final-recipes.yaml` based on synergistic-oils.yaml template
  - [ ] 2.2 Configure prompt for gpt-4.1-nano model with time-of-day variable and JSON schema validation
  - [ ] 2.3 Implement `src/features/create-recipe/utils/recipe-data-transformer.ts` to convert wizard data to AI format
  - [ ] 2.4 Add safety filtering logic in `src/features/create-recipe/utils/safety-filter.ts` for children under 10
  - [ ] 2.5 Create `src/features/create-recipe/hooks/use-final-recipes-generation.ts` for parallel AI calls
  - [ ] 2.6 Implement streaming responses with retry logic (up to 2 retries per failed recipe)
  - [ ] 2.7 Add JSON schema validation using Zod schemas for AI response validation
  - [ ] 2.8 Create mock data matching json-dataset-minimal.json structure for development and testing
  - [ ] 2.9 Write comprehensive unit tests for all AI generation utilities and hooks

- [ ] 3.0 Create Frontend Components and UI
  - [ ] 3.1 Create `src/features/create-recipe/components/final-recipes-display.tsx` as main container component
  - [ ] 3.2 Implement responsive layout following standalone-v1.html design using theme variables only
  - [ ] 3.3 Create `src/features/create-recipe/components/recipe-protocol-card.tsx` for individual time-slot recipes
  - [ ] 3.4 Add collapsible sections, ingredient lists, and visual droplet animations to recipe cards
  - [ ] 3.5 Implement `src/features/create-recipe/components/container-recommendation.tsx` with size/type/dispenser logic
  - [ ] 3.6 Create `src/features/create-recipe/components/safety-warnings.tsx` for demographic-specific warnings
  - [ ] 3.7 Add loading states and error handling UI for failed recipe generations
  - [ ] 3.8 Ensure all components stay under 500 lines following DRY and KISS principles
  - [ ] 3.9 Write comprehensive unit tests for all UI components with React Testing Library

- [ ] 4.0 Implement State Management and Navigation
  - [ ] 4.1 Extend Zustand store in `src/features/create-recipe/store/recipe-store.ts` with final recipes state
  - [ ] 4.2 Add actions for storing generated recipes, managing loading states, and handling errors
  - [ ] 4.3 Implement data access methods to retrieve oil IDs, property IDs, and safety data from previous steps
  - [ ] 4.4 Update `src/features/create-recipe/components/wizard-container.tsx` to include final recipes step
  - [ ] 4.5 Modify `src/features/create-recipe/hooks/use-recipe-navigation.ts` to prevent back navigation from final step
  - [ ] 4.6 Update step validation logic to ensure data completeness before reaching final step
  - [ ] 4.7 Implement session-only data storage (no database persistence for MVP)
  - [ ] 4.8 Write comprehensive unit tests for store extensions and navigation logic

- [ ] 5.0 Add Internationalization and Testing
  - [ ] 5.1 Add translation keys to `src/locales/en/create-recipe.json` for all final step UI text
  - [ ] 5.2 Add corresponding translations to `src/locales/es/create-recipe.json` and `src/locales/pt/create-recipe.json`
  - [ ] 5.3 Ensure all user-facing text uses _localized keys from AI responses
  - [ ] 5.4 Implement oil name display in both localized and botanical names
  - [ ] 5.5 Add measurement unit consistency (drops and ml only, no locale-based units)
  - [ ] 5.6 Create comprehensive integration tests for the complete final step workflow
  - [ ] 5.7 Add end-to-end tests covering parallel recipe generation and error scenarios
  - [ ] 5.8 Verify 100% test coverage for all new code and maintain existing coverage
  - [ ] 5.9 Test responsive design across all target devices and ensure accessibility compliance
