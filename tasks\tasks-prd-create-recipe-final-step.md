# Task List: Create Recipe Final Step Implementation

## Relevant Files

- `src/features/create-recipe/types/recipe.types.ts` - Add new types for final step data structures and recipe protocols with granular state tracking
- `src/features/create-recipe/types/recipe.types.test.ts` - Unit tests for new type definitions
- `src/features/create-recipe/constants/recipe.constants.ts` - Update step definitions to replace OILS step with FINAL_RECIPES step
- `src/features/create-recipe/store/recipe-store.ts` - Extend store with final step state management and granular recipe status tracking
- `src/features/create-recipe/store/recipe-store.test.ts` - Unit tests for store extensions
- `src/features/create-recipe/schemas/recipe.schemas.ts` - Centralized Zod schemas for all recipe data validation (health concern, demographics, causes, symptoms, properties, final recipes)
- `src/features/create-recipe/schemas/recipe.schemas.test.ts` - Unit tests for centralized schema validation
- `src/features/create-recipe/prompts/final-recipes.yaml` - AI prompt template for recipe generation with _localized key strategy
- `src/features/create-recipe/components/final-recipes-display.tsx` - Main container component managing data fetching and state
- `src/features/create-recipe/components/final-recipes-display.test.tsx` - Unit tests for main final recipes component
- `src/features/create-recipe/components/recipe-protocol-card.tsx` - Presentational component for individual recipe cards with props-based data flow
- `src/features/create-recipe/components/recipe-protocol-card.test.tsx` - Unit tests for recipe card component
- `src/features/create-recipe/components/container-recommendation.tsx` - Pure presentational component for container recommendations
- `src/features/create-recipe/components/container-recommendation.test.tsx` - Unit tests for container recommendation component
- `src/features/create-recipe/components/safety-warnings.tsx` - Pure presentational component for safety warnings and rationale
- `src/features/create-recipe/components/safety-warnings.test.tsx` - Unit tests for safety warnings component
- `src/features/create-recipe/hooks/use-final-recipes-generation.ts` - Hook for managing parallel AI recipe generation with individual error handling
- `src/features/create-recipe/hooks/use-final-recipes-generation.test.ts` - Unit tests for recipe generation hook
- `src/features/create-recipe/utils/recipe-data-transformer.ts` - Utility for transforming wizard data to AI-ready format with Zod validation
- `src/features/create-recipe/utils/recipe-data-transformer.test.ts` - Unit tests for data transformation utilities
- `src/features/create-recipe/utils/safety-filter.ts` - Utility for filtering dermocaustic oils based on demographics
- `src/features/create-recipe/utils/safety-filter.test.ts` - Unit tests for safety filtering logic
- `src/features/create-recipe/utils/error-handler.ts` - Utility for handling specific error scenarios and user feedback messages
- `src/features/create-recipe/utils/error-handler.test.ts` - Unit tests for error handling utilities
- `src/features/create-recipe/components/wizard-container.tsx` - Update to include final recipes step in step renderer
- `src/features/create-recipe/hooks/use-recipe-navigation.ts` - Update navigation logic to handle final step restrictions
- `src/locales/en/create-recipe.json` - English translations for final step UI text and error messages
- `src/locales/es/create-recipe.json` - Spanish translations for final step UI text and error messages
- `src/locales/pt/create-recipe.json` - Portuguese translations for final step UI text and error messages

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Follow existing patterns in `src/features/create-recipe` for consistency
- Ensure all components stay under 500 lines following DRY and KISS principles
- Mock data should match `docs/create-recipe/new-final-step/json-dataset-minimal.json` structure

## Tasks

- [ ] 1.0 Update Core Architecture and Types
  - [ ] 1.1 Add FINAL_RECIPES enum value to RecipeStep in `src/features/create-recipe/types/recipe.types.ts`
  - [ ] 1.2 Create FinalRecipeProtocol, RecipeTimeSlot, ContainerRecommendation, and SafetyWarning interfaces
  - [ ] 1.3 Create FinalRecipeStatus interface with granular state tracking ('idle' | 'loading' | 'success' | 'error')
  - [ ] 1.4 Add FinalRecipesState interface with individual recipe status tracking for morning/mid-day/night protocols
  - [ ] 1.5 Create centralized Zod schemas in `src/features/create-recipe/schemas/recipe.schemas.ts` for all wizard data validation
  - [ ] 1.6 Update WIZARD_STEPS constant in `src/features/create-recipe/constants/recipe.constants.ts` to replace OILS with FINAL_RECIPES
  - [ ] 1.7 Update step configuration with proper path, title, and navigation properties for final step
  - [ ] 1.8 Write comprehensive unit tests for all new type definitions, schemas, and constants

- [ ] 2.0 Implement AI Recipe Generation System
  - [ ] 2.1 Create `src/features/create-recipe/prompts/final-recipes.yaml` based on synergistic-oils.yaml template
  - [ ] 2.2 Configure prompt for gpt-4.1-nano model with time-of-day variable and _localized key strategy for dynamic content
  - [ ] 2.3 Implement `src/features/create-recipe/utils/recipe-data-transformer.ts` with Zod validation for wizard data conversion
  - [ ] 2.4 Add safety filtering logic in `src/features/create-recipe/utils/safety-filter.ts` for children under 10
  - [ ] 2.5 Create `src/features/create-recipe/hooks/use-final-recipes-generation.ts` for parallel AI calls with individual error tracking
  - [ ] 2.6 Implement streaming responses with granular retry logic (up to 2 retries per failed recipe)
  - [ ] 2.7 Add comprehensive error handling with specific error messages for each recipe failure scenario
  - [ ] 2.8 Create `src/features/create-recipe/utils/error-handler.ts` for centralized error message management
  - [ ] 2.9 Add JSON schema validation using centralized Zod schemas for AI response validation
  - [ ] 2.10 Create mock data matching json-dataset-minimal.json structure for development and testing
  - [ ] 2.11 Write comprehensive unit tests for all AI generation utilities, hooks, and error handling

- [ ] 3.0 Create Frontend Components and UI
  - [ ] 3.1 Create `src/features/create-recipe/components/final-recipes-display.tsx` as main container component managing data fetching and state
  - [ ] 3.2 Implement data flow from useRecipeStore and use-final-recipes-generation hook to child components
  - [ ] 3.3 Implement responsive layout following standalone-v1.html design using theme variables only
  - [ ] 3.4 Create `src/features/create-recipe/components/recipe-protocol-card.tsx` as presentational component with props-based data flow
  - [ ] 3.5 Add collapsible sections, ingredient lists, and visual droplet animations to recipe cards
  - [ ] 3.6 Implement `src/features/create-recipe/components/container-recommendation.tsx` as pure presentational component
  - [ ] 3.7 Create `src/features/create-recipe/components/safety-warnings.tsx` as pure presentational component
  - [ ] 3.8 Add granular loading states for each recipe (morning/mid-day/night) with individual status indicators
  - [ ] 3.9 Implement specific error handling UI for individual recipe failures with actionable error messages
  - [ ] 3.10 Add global error message display when all three recipe generations fail
  - [ ] 3.11 Ensure all components stay under 500 lines following DRY and KISS principles with clear separation of concerns
  - [ ] 3.12 Write comprehensive unit tests for all UI components with React Testing Library

- [ ] 4.0 Implement State Management and Navigation
  - [ ] 4.1 Extend Zustand store in `src/features/create-recipe/store/recipe-store.ts` with granular final recipes state tracking
  - [ ] 4.2 Implement FinalRecipesState with individual status tracking for morning/mid-day/night protocols
  - [ ] 4.3 Add actions for storing generated recipes, managing granular loading states, and handling individual errors
  - [ ] 4.4 Implement data access methods to retrieve oil IDs, property IDs, and safety data from previous steps with Zod validation
  - [ ] 4.5 Add state management for retry attempts and error recovery for each recipe protocol
  - [ ] 4.6 Update `src/features/create-recipe/components/wizard-container.tsx` to include final recipes step
  - [ ] 4.7 Modify `src/features/create-recipe/hooks/use-recipe-navigation.ts` to prevent back navigation from final step
  - [ ] 4.8 Update step validation logic to ensure data completeness before reaching final step using centralized schemas
  - [ ] 4.9 Implement session-only data storage (no database persistence for MVP)
  - [ ] 4.10 Write comprehensive unit tests for store extensions, state management, and navigation logic

- [ ] 5.0 Add Internationalization and Testing
  - [ ] 5.1 Add translation keys to `src/locales/en/create-recipe.json` for all final step UI text and specific error messages
  - [ ] 5.2 Add corresponding translations to `src/locales/es/create-recipe.json` and `src/locales/pt/create-recipe.json`
  - [ ] 5.3 Configure AI prompt to generate _localized keys for dynamic content (recipe names, descriptions, rationale)
  - [ ] 5.4 Implement fallback strategy for static text internationalization using useTranslation hook
  - [ ] 5.5 Implement oil name display in both localized and botanical names following existing patterns
  - [ ] 5.6 Add measurement unit consistency (drops and ml only, no locale-based units)
  - [ ] 5.7 Create comprehensive integration tests for the complete final step workflow with granular state testing
  - [ ] 5.8 Add end-to-end tests covering parallel recipe generation, individual failures, and retry scenarios
  - [ ] 5.9 Add specific tests for error handling scenarios (single recipe failure, multiple failures, all failures)
  - [ ] 5.10 Verify 100% test coverage for all new code including centralized schemas and error handling
  - [ ] 5.11 Test responsive design across all target devices and ensure accessibility compliance
  - [ ] 5.12 Validate component hierarchy and data flow patterns through integration testing
