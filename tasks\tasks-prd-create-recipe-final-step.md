# Task List: Create Recipe Final Step Implementation

## Relevant Files

- `src/features/create-recipe/types/recipe.types.ts` - Add new types for final step data structures and recipe protocols
- `src/features/create-recipe/types/recipe.types.test.ts` - Unit tests for new type definitions
- `src/features/create-recipe/constants/recipe.constants.ts` - Update step definitions to replace OILS step with FINAL_RECIPES step
- `src/features/create-recipe/store/recipe-store.ts` - Extend store with final step state management and recipe data
- `src/features/create-recipe/store/recipe-store.test.ts` - Unit tests for store extensions
- `src/features/create-recipe/prompts/final-recipes.yaml` - AI prompt template for recipe generation based on synergistic-oils.yaml
- `src/features/create-recipe/components/final-recipes-display.tsx` - Main component for final step displaying three recipe protocols
- `src/features/create-recipe/components/final-recipes-display.test.tsx` - Unit tests for main final recipes component
- `src/features/create-recipe/components/recipe-protocol-card.tsx` - Individual recipe card component (morning/mid-day/night)
- `src/features/create-recipe/components/recipe-protocol-card.test.tsx` - Unit tests for recipe card component
- `src/features/create-recipe/components/container-recommendation.tsx` - Component for container and dispensing system recommendations
- `src/features/create-recipe/components/container-recommendation.test.tsx` - Unit tests for container recommendation component
- `src/features/create-recipe/components/safety-warnings.tsx` - Component for displaying safety warnings and rationale
- `src/features/create-recipe/components/safety-warnings.test.tsx` - Unit tests for safety warnings component
- `src/features/create-recipe/hooks/use-final-recipes-generation.ts` - Hook for managing parallel AI recipe generation
- `src/features/create-recipe/hooks/use-final-recipes-generation.test.ts` - Unit tests for recipe generation hook
- `src/features/create-recipe/utils/recipe-data-transformer.ts` - Utility for transforming wizard data to AI-ready format
- `src/features/create-recipe/utils/recipe-data-transformer.test.ts` - Unit tests for data transformation utilities
- `src/features/create-recipe/utils/safety-filter.ts` - Utility for filtering dermocaustic oils based on demographics
- `src/features/create-recipe/utils/safety-filter.test.ts` - Unit tests for safety filtering logic
- `src/features/create-recipe/schemas/final-recipes-schemas.ts` - Zod schemas for validating AI responses and recipe data
- `src/features/create-recipe/schemas/final-recipes-schemas.test.ts` - Unit tests for schema validation
- `src/features/create-recipe/components/wizard-container.tsx` - Update to include final recipes step in step renderer
- `src/features/create-recipe/hooks/use-recipe-navigation.ts` - Update navigation logic to handle final step restrictions
- `src/locales/en/create-recipe.json` - English translations for final step UI text
- `src/locales/es/create-recipe.json` - Spanish translations for final step UI text  
- `src/locales/pt/create-recipe.json` - Portuguese translations for final step UI text

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Follow existing patterns in `src/features/create-recipe` for consistency
- Ensure all components stay under 500 lines following DRY and KISS principles
- Mock data should match `docs/create-recipe/new-final-step/json-dataset-minimal.json` structure

## Tasks

- [ ] 1.0 Update Core Architecture and Types
- [ ] 2.0 Implement AI Recipe Generation System  
- [ ] 3.0 Create Frontend Components and UI
- [ ] 4.0 Implement State Management and Navigation
- [ ] 5.0 Add Internationalization and Testing
