# 🌍 Namespace-Based Internationalization System

A comprehensive, production-ready i18n solution with server-side support and AI-friendly translation workflow.

## ✨ Features

- **Namespace-based**: Organized by feature domains (`create-recipe:`, `dashboard:`, `common:`)
- **Server-side support**: Full SSR/SSG compatibility with locale detection
- **Kebab-case standardization**: Consistent naming convention for step keys
- **AI-friendly**: Designed for seamless AI translation workflow with metadata
- **Type-safe**: Full TypeScript support with strict type checking
- **Flexible**: Support for nested keys, variable interpolation, and fallbacks
- **Production-ready**: Battle-tested with comprehensive error handling

## 🚀 Quick Start

### 1. Client-side Components

```tsx
import { useI18n } from '@/hooks/use-i18n';

export function MyComponent() {
  const { t, locale, isPortuguese } = useI18n();

  return (
    <div>
      {/* Namespace-based translation keys */}
      <h1>{t('create-recipe:title')}</h1>
      <button>{t('common:buttons.continue')}</button>

      {/* Step keys use kebab-case */}
      <h2>{t('create-recipe:steps.health-concern.title')}</h2>

      {/* With variables */}
      <p>{t('create-recipe:navigation.progress', undefined, {
        current: 2,
        total: 5
      })}</p>
    </div>
  );
}
```

### 2. Server-side Components (Next.js)

```tsx
import { getServerLocale, getLocalizedMetadata } from '@/lib/i18n/utils/server-language-utils';

export async function generateMetadata() {
  const locale = await getServerLocale();
  const metadata = getLocalizedMetadata(locale);

  return {
    title: metadata.recipeCreator,
    description: metadata.description
  };
}
```

## 📁 File Structure

```
src/
├── lib/i18n/
│   ├── index.ts                    # Core i18n system
│   ├── utils/
│   │   ├── language-utils.ts       # Client-side language utilities
│   │   └── server-language-utils.ts # Server-side locale detection
│   ├── messages/                   # Namespace-based translations
│   │   ├── en/                     # English (base language)
│   │   │   ├── common.json         # Shared UI elements
│   │   │   ├── auth.json           # Authentication
│   │   │   ├── create-recipe.json  # Recipe creation wizard
│   │   │   ├── dashboard.json      # Dashboard navigation
│   │   │   └── homepage.json       # Public homepage
│   │   ├── pt/                     # Portuguese (Brazil)
│   │   │   └── ... (same structure)
│   │   └── es/                     # Spanish (Latin America)
│   │       └── ... (same structure)
│   └── prompts/                    # AI translation prompts
│       ├── ai-translation.yaml
│       └── translation-validation.yaml
└── hooks/
    └── use-i18n.ts                 # Global i18n hook
```

## 🤖 AI Translation Workflow

### Step 1: Generate Translation Files

```bash
node scripts/generate-translation-files.js
```

### Step 2: Send to AI

Copy the generated `pt.json` or `es.json` files and send to AI with this prompt:

```
Please translate this JSON file. Replace all "TRANSLATE: " prefixes with the actual translation in [Portuguese/Spanish]. Keep the JSON structure and variable placeholders like {current}, {total} unchanged. Context: Essential oils and aromatherapy web application.
```

### Step 3: Validate & Deploy

- Review the translations
- Replace the generated files with AI-translated versions
- Test in your application

## 🔧 Supported Languages

- **English** (`en`) - Base language
- **Portuguese (Brazil)** (`pt`) - Main target audience
- **Spanish (Latin America)** (`es`) - Secondary target audience

## 🎯 Translation Keys Structure & Naming Conventions

### Namespace-based Organization
Each feature has its own namespace to prevent key conflicts:

```json
// src/lib/i18n/messages/en/create-recipe.json
{
  "title": "Essential Oil Recipe Creator",
  "subtitle": "Get personalized essential oil recommendations",
  "steps": {
    "health-concern": {
      "title": "Health Concern",
      "description": "Tell us about your health concern or goal"
    },
    "demographics": {
      "title": "Demographics",
      "description": "Help us personalize your recommendations"
    },
    "causes": {
      "title": "Potential Causes",
      "description": "Select what might be contributing"
    }
  },
  "navigation": {
    "progress": "Step {current} of {total}",
    "currentStepAnnouncement": "Currently on {title}, step {progress} of 6"
  }
}
```

### Key Naming Conventions

1. **Step Keys**: Use kebab-case to match `RecipeStep` enum values
   ```typescript
   // ✅ Correct
   t('create-recipe:steps.health-concern.title')
   t('create-recipe:steps.demographics.title')

   // ❌ Wrong
   t('create-recipe:steps.healthConcern.title')
   ```

2. **Namespace Syntax**: Always use colon separator
   ```typescript
   // ✅ Correct
   t('common:buttons.continue')
   t('dashboard:sidebar.ai.title')

   // ❌ Wrong
   t('common.buttons.continue')
   ```

## 🌐 Locale Detection & Server-side Support

### Client-side Locale Detection
```tsx
import { useUserLanguage, useApiLanguage } from '@/lib/i18n/utils';

// Get user's preferred language
const userLanguage = useUserLanguage(); // 'pt', 'en', 'es'

// Get API-formatted language for backend calls
const apiLanguage = useApiLanguage(); // 'PT_BR', 'EN_US', 'ES_ES'
```

### Server-side Locale Detection
```tsx
import { getServerLocale, getServerApiLanguage } from '@/lib/i18n/utils/server-language-utils';

// In server components or API routes
const locale = await getServerLocale(); // 'pt', 'en', 'es'
const apiLanguage = await getServerApiLanguage(); // 'PT_BR', 'EN_US', 'ES_ES'
```

### Locale Detection Priority Order
1. **x-locale header** (set by middleware or client)
2. **locale cookie** (user preference)
3. **accept-language header** (browser preference)
4. **default fallback** ('en')

## 📈 Gradual Migration Strategy

1. **Start with key components**: Begin with the most important user-facing text
2. **Use fallbacks**: Always provide English fallback text
3. **Test incrementally**: Test each component as you add translations
4. **Expand gradually**: Add more translation keys as needed

## 🛠 Example Migration

### Before:
```tsx
<button>Continue to Next Step</button>
<h1>Essential Oil Recipe Creator</h1>
```

### After:
```tsx
<button>{t('common.buttons.continue', 'Continue to Next Step')}</button>
<h1>{t('createRecipe.title', 'Essential Oil Recipe Creator')}</h1>
```

## 💡 Best Practices

1. **No fallbacks in production**: Remove fallback text once translations are complete
   ```tsx
   // ✅ Production-ready
   t('create-recipe:steps.health-concern.title')

   // ❌ Development only
   t('create-recipe:steps.health-concern.title', 'Health Concern')
   ```

2. **Use kebab-case for step keys**: Match the `RecipeStep` enum values
   ```tsx
   // ✅ Correct
   t('create-recipe:steps.health-concern.title')

   // ❌ Wrong
   t('create-recipe:steps.healthConcern.title')
   ```

3. **Namespace organization**: Group by feature domain
   ```
   common:     Shared UI elements (buttons, labels, errors)
   auth:       Authentication flows
   dashboard:  Navigation and dashboard elements
   create-recipe: Recipe creation wizard
   homepage:   Public marketing content
   ```

4. **Consistent key hierarchies**: Follow established patterns
   ```json
   {
     "steps": {
       "step-name": {
         "title": "...",
         "description": "...",
         "placeholder": "..."
       }
     },
     "navigation": {
       "progress": "...",
       "buttons": { "next": "...", "previous": "..." }
     }
   }
   ```

5. **Test all languages**: Always verify translations in all supported locales

## 🔍 Troubleshooting

### Common Issues

1. **English text showing in Portuguese/Spanish**
   ```bash
   # Check if translation key exists
   grep -r "health-concern" src/lib/i18n/messages/pt/

   # Verify namespace usage
   t('create-recipe:steps.health-concern.title') # ✅ Correct
   t('steps.health-concern.title')               # ❌ Missing namespace
   ```

2. **Translation key not found**
   ```typescript
   // Check console for warnings
   console.warn('Translation key not found: create-recipe:steps.missing-key.title')

   // Verify key exists in all language files
   src/lib/i18n/messages/en/create-recipe.json
   src/lib/i18n/messages/pt/create-recipe.json
   src/lib/i18n/messages/es/create-recipe.json
   ```

3. **Server-side locale detection issues**
   ```typescript
   // Debug locale detection
   const locale = await getServerLocale();
   console.log('Detected locale:', locale);

   // Check headers
   const headersList = await headers();
   console.log('x-locale header:', headersList.get('x-locale'));
   ```

### Translation Loading Errors
The system will automatically fallback in this order:
1. Current locale translation
2. English translation
3. Translation key as display text

## ➕ Adding New Translations

### 1. Create New Namespace
```bash
# Create new namespace files
touch src/lib/i18n/messages/en/my-feature.json
touch src/lib/i18n/messages/pt/my-feature.json
touch src/lib/i18n/messages/es/my-feature.json
```

### 2. Add Translation Keys
```json
// src/lib/i18n/messages/en/my-feature.json
{
  "_context": "My feature translations",
  "title": "My Feature Title",
  "steps": {
    "step-one": {
      "title": "Step One",
      "description": "Description for step one"
    }
  },
  "actions": {
    "save": "Save Changes",
    "cancel": "Cancel"
  }
}
```

### 3. Use in Components
```tsx
import { useI18n } from '@/hooks/use-i18n';

export function MyFeature() {
  const { t } = useI18n();

  return (
    <div>
      <h1>{t('my-feature:title')}</h1>
      <h2>{t('my-feature:steps.step-one.title')}</h2>
      <button>{t('my-feature:actions.save')}</button>
    </div>
  );
}
```

### 4. Translation Validation
Ensure all language files have equivalent keys:
```bash
# Check key consistency across languages
npm run i18n:validate  # (if validation script exists)
```

## 📚 Documentation Structure

### Core Documentation
- **[README.md](./README.md)** - This file: Complete implementation guide
- **[i18n Guidelines](./.cursor/rules/i18n-guidelines.mdc)** - Development rules and conventions

### Related Documentation
- **[Server Language Utils](./utils/server-language-utils.ts)** - Server-side locale detection
- **[Client Language Utils](./utils/language-utils.ts)** - Client-side language utilities
- **[Translation Files](./messages/)** - Namespace-organized translation files

### Key Implementation Files
```
src/lib/i18n/
├── index.ts                    # Core i18n system
├── utils/
│   ├── language-utils.ts       # Client-side utilities
│   └── server-language-utils.ts # Server-side utilities
├── messages/                   # Translation files
│   ├── en/                     # English (base)
│   ├── pt/                     # Portuguese
│   └── es/                     # Spanish
└── README.md                   # This documentation
```

## 🎯 Quick Reference

### Essential Patterns
```typescript
// Client-side component
const { t } = useI18n();
t('create-recipe:steps.health-concern.title')

// Server-side metadata
const locale = await getServerLocale();
const metadata = getLocalizedMetadata(locale);

// Step keys (kebab-case)
t('create-recipe:steps.health-concern.title')  // ✅ Correct
t('create-recipe:steps.healthConcern.title')   // ❌ Wrong
```

### Common Issues
1. **English in Portuguese**: Check translation key exists
2. **Key not found**: Verify namespace syntax (`namespace:key`)
3. **Case mismatch**: Use kebab-case for step keys
4. **Server metadata**: Use `getServerLocale()` utility

## 🌐 App-Wide Usage Examples

### Authentication
```tsx
// Login form
<h1>{t('auth.login.title', 'Welcome Back')}</h1>
<input placeholder={t('auth.login.emailPlaceholder', 'Enter your email')} />
```

### Dashboard
```tsx
// Dashboard header
<h1>{t('dashboard.title', 'Dashboard')}</h1>
<p>{t('dashboard.welcome', 'Welcome back')}</p>
```

### Navigation
```tsx
// Navigation menu
<nav>
  <a href="/dashboard">{t('common.navigation.dashboard', 'Dashboard')}</a>
  <a href="/profile">{t('common.navigation.profile', 'Profile')}</a>
</nav>
```

---

**Ready to internationalize your entire application! 🎉** 