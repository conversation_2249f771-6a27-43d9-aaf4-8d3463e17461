# Product Requirements Document: Create Recipe Final Step (v2)

## 1. Introduction/Overview
This document outlines the requirements for the final step of the "Create Recipe" feature. This step is responsible for taking the user's comprehensive health profile, gathered through the preceding steps, and generating three distinct, personalized essential oil protocols for morning, mid-day, and night.

**Problem Statement:** Users need a concrete, safe, and professionally structured essential oil recipe based on their specific health inputs. The current workflow collects data but lacks the final, actionable output.

**Goal:** To dynamically generate and display three parallel, time-specific essential oil protocols with precise measurements, safety guidelines, and comprehensive usage instructions, seamlessly integrated as the final step of the recipe creation process.

## 2. Goals
- **Primary Goal:** Replace the unused "Oils" step with a new, dynamic final step that generates three AI-powered recipes in parallel.
- **Safety Goal:** Ensure 100% safety compliance by programmatically filtering dermocaustic oils for child users and applying correct, conservative dilution ratios.
- **Usability Goal:** Deliver clear, actionable, and easy-to-understand protocols, including exact measurements, container recommendations, and application instructions suitable for a novice user.
- **Performance Goal:** Implement parallel AI generation to ensure the three recipes are delivered to the user in under 3 seconds.
- **Integration Goal:** Ensure the new step integrates flawlessly into the existing multi-step workflow (`src/features/create-recipe`) without breaking changes.

## 3. User Stories
- **As a user**, I want to see a summary of my health profile analysis so I can understand the therapeutic strategy behind my recipe.
- **As a user**, I want a complete recipe with exact measurements so I can prepare it safely and accurately.
- **As a user**, I want detailed usage instructions, including timing and frequency, so I can follow a professional protocol.
- **As a user**, I want to know exactly what container to buy (including size and type) and how to prepare it so I can create the recipe properly.
- **As a user**, I want a brief explanation for why specific oils were chosen so I can trust the recommendation.
- **As a user**, I want to receive specific protocols for different times of the day (morning, mid-day, night) to fit into my daily routine.

## 4. Functional Requirements

### 4.1 Data Input & Processing
- **FR-1:** The system must receive and process user data matching the structure defined in `docs/create-recipe/new-final-step/json-dataset-minimal.json`.
- **FR-2:** The system must transform the collected user health profile, demographics, and preferences into a format suitable for the AI agent.

### 4.2 AI Recipe Generation
- **FR-3:** The system **must** use the OpenAI Agents JS SDK, following existing patterns in `src/lib/ai`. Direct API calls are not permitted.
- **FR-4:** The system must generate 3 distinct recipes in parallel: one for a morning protocol, one for a mid-day protocol, and one for a night protocol.
- **FR-5:** The system **must** implement safety logic to filter out dermocaustic oils from the candidate list if the user's demographic profile indicates they are a child.
- **FR-6:** The AI prompt implementation must be based on the templates and examples found in `docs/create-recipe/new-final-step/08 - synergistic-oils.yaml` and other prompts in `src/features/create-recipe/prompts`.
- **FR-7:** For each recipe, the AI must generate a short "rationale" sentence explaining why the oils were selected, building upon the existing rationale data in the dataset.

### 4.3 Dynamic Frontend Generation
- **FR-8:** The UI must be dynamically generated based on an AI-produced JSON schema, following the visual layout of `docs/create-recipe/new-final-step/standalone-v1.html`.
- **FR-9:** All UI components must use the project's existing theme variables. No hardcoded or custom styles are permitted.
- **FR-10:** All fields shown in the `standalone-v1.html` mockup are mandatory for the MVP, with the exception of the "Segurança" (Safety) tab, which will contain static, pre-defined content.
- **FR-11:** The "Container & Dispensing System" recommendations must be specific, including **size** (e.g., 10ml), **container type** (e.g., roller bottle), and **dispenser type** (e.g., rollerball). The AI will determine the appropriate combination based on the recipe's intended use.

### 4.4 Error Handling & State
- **FR-12:** If any of the three parallel AI recipe generations fail, the system must automatically retry the failed generation up to **two times**.
- **FR-13:** If a recipe generation continues to fail after retries, the UI must display an error message for that specific recipe section while still displaying any recipes that were generated successfully.
- **FR-14:** The generated recipe data is for in-session viewing only and does not need to be persisted to the database in this version.

### 4.5 Internationalization (i18n)
- **FR-15:** All static text and labels in the UI must support internationalization for English (en), Spanish (es), and Portuguese (pt), following the guidelines in `.cursor/rules/i18n-guidelines.mdc`.

## 5. Non-Goals (Out of Scope for this MVP)
- **User-driven Regeneration:** Users will not have an option to regenerate recipes. Generation is a one-time event per session.
- **Recipe Editing:** Users cannot edit the generated recipes. This will be considered for a future phase.
- **Saving/Persisting Recipes:** The ability for a user to save a generated recipe to their profile will be included in Phase 2.
- **Oil Substitution:** A feature to find and substitute alternative oils is planned for Phase 3.
- **Advanced Features:** Integration with scientific studies, deep-linking to oil profiles, an interactive dilution calculator, and holistic wellness tabs are all planned for future phases, not the MVP.

## 6. Design Considerations
- **UI Reference:** The primary design reference is `docs/create-recipe/new-final-step/standalone-v1.html`.
- **Responsiveness:** A mobile-first design approach is required, as 90% of users are on mobile devices.
- **Consistency:** The final step must maintain visual and interactive consistency with the preceding steps in the `create-recipe` workflow.

## 7. Technical Considerations
- **Integration:** This feature will replace the `RecipeStep.OILS` enum and view in the existing navigation and state management logic.
- **State Management:** Use the existing Zustand store to manage the state of the final step, consistent with other steps. The data should be cleared upon exiting the workflow.
- **Performance:** Parallel API calls are critical. The implementation should mirror existing patterns for parallel processing within the application to meet the sub-3-second goal.

## 8. Success Metrics
- **Technical:** 100% test coverage for the AI integration logic. Generation of all three recipes completes in under 3 seconds.
- **Functional:** The feature is successfully integrated, replacing the old "Oils" step without introducing regressions. All i18n strings are correctly implemented and displayed.
- **Safety:** Zero critical bugs related to incorrect oil filtering or dilution calculations for child users.
- **User Experience:** The final UI matches the `standalone-v1.html` mockup on all target devices and is fully responsive.