# Advanced Authentication Architecture

This document provides a comprehensive overview of the **enterprise-grade authentication system** implemented in this Next.js application. Our architecture leverages advanced optimizations including performance monitoring, connection resilience, error recovery mechanisms, and sophisticated caching strategies with a **DRY (Don't Repeat Yourself) approach**.

## 🏗️ Architecture Overview

Our advanced authentication system is built on these core principles:

1. **Single Source of Truth**: The enhanced `useAuth` hook provides all authentication data with advanced features
2. **DRY Architecture**: Centralized server auth utilities prevent code duplication across layouts
3. **Performance Monitoring**: Real-time tracking of render counts, fetch times, and performance metrics
4. **Connection Resilience**: Offline-first networking with automatic retry and background sync
5. **Error Recovery**: Exponential backoff retry mechanisms with user-initiated recovery
6. **Advanced Caching**: Stale-while-revalidate patterns with intelligent cache invalidation
7. **Comprehensive Observability**: Enhanced Sentry integration with performance context

## 🔧 Core Components

### 1. Enhanced `useAuth` Hook - The Central Authority

**Location**: `@/features/auth/hooks/use-auth.ts`

**Purpose**: Enterprise-grade authentication interface with advanced optimizations.

**Advanced Features**:
- **Performance Monitoring**: Real-time render tracking and performance metrics
- **Error Recovery**: Manual retry and clear error functions with exponential backoff
- **Connection Awareness**: Automatic retry when connection is restored
- **Enhanced Logging**: State change tracking with performance context
- **Memory Optimization**: Stable references and optimized dependencies

**Enhanced Interface**:
```typescript
interface AuthState {
  user: User | null;
  profile: UserProfile | null | undefined;
  authUser: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  profileError: Error | null;
  // Performance monitoring (debug mode only)
  _performance?: {
    renderCount: number;
    lastRenderTime: number;
  };
  // Error recovery utilities
  retry: () => void;
  clearErrors: () => void;
}
```

**Usage Pattern**:
```typescript
const { 
  user, 
  profile, 
  isAuthenticated, 
  isLoading, 
  retry, 
  clearErrors,
  _performance 
} = useAuth();

// Error recovery
if (profileError) {
  return (
    <div>
      <p>Failed to load profile</p>
      <button onClick={retry}>Retry</button>
      <button onClick={clearErrors}>Clear Errors</button>
    </div>
  );
}
```

### 2. Centralized Server Auth Utility - DRY Architecture

**Location**: `@/lib/auth/server-auth.utils.ts`

**Purpose**: Single source of truth for server-side authentication and profile prefetching across all layouts.

**Key Features**:
- **DRY Implementation**: Eliminates code duplication across layouts
- **Request-Level Caching**: Uses existing cached services (no duplication)
- **Configurable Options**: Timeout, debug mode, prefetch settings
- **Consistent Error Handling**: Standardized error patterns across layouts
- **Performance Optimized**: Race conditions and timeout protection

**Usage Pattern**:
```typescript
// Any layout can use this centralized utility
const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
  prefetchProfile: true,
  profileTimeout: 500,
  profileStaleTime: 10 * 1000,
  requestId: `layout-${Date.now()}`,
  debugMode: shouldDebugServer
});

// Returns consistent structure for all layouts
return (
  <HydrationBoundary state={dehydratedState}>
    {children}
  </HydrationBoundary>
);
```

**Benefits**:
- ✅ **Single Implementation**: No more duplicated auth logic across layouts
- ✅ **Consistent Behavior**: Same auth patterns everywhere
- ✅ **Easy Maintenance**: Update auth logic in one place
- ✅ **Performance**: Reuses existing cached services
- ✅ **Error Handling**: Standardized error patterns

### 3. Advanced Profile Query Hook

**Location**: `@/features/user-auth-data/hooks/use-user-profile-query.ts`

**Enterprise Features**:
- **Performance Metrics**: Fetch count, average time, cache hit tracking
- **Background Sync**: Automatic data refresh every 5 minutes
- **Connection Resilience**: Online/offline detection with automatic retry
- **Cache Management**: Manual invalidation and prefetch utilities
- **Enhanced Options**: Configurable retry, stale time, and sync behavior

**Advanced Configuration**:
```typescript
interface UseUserProfileQueryOptions {
  enabled?: boolean;
  retry?: boolean | number | ((failureCount: number, error: any) => boolean);
  retryDelay?: number | ((attemptIndex: number) => number);
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  // Enhanced options
  backgroundSync?: boolean;
  optimisticUpdates?: boolean;
  prefetchRelated?: boolean;
}
```

**Enhanced Return Value**:
```typescript
const {
  data,
  isLoading,
  error,
  // Performance metrics (debug mode only)
  _performance,
  // Cache utilities
  invalidateCache,
  prefetch,
  // Connection state
  isOnline
} = useUserProfileQuery(userId, {
  backgroundSync: true,
  refetchOnReconnect: true,
  retry: (failureCount, error) => failureCount < 3,
  retryDelay: (attemptIndex) => Math.min(1000 * Math.pow(2, attemptIndex), 10000)
});
```

### 4. Enhanced Auth Session Provider

**Location**: `@/providers/auth-session-provider.tsx`

**Advanced Optimizations**:
- **Sentry Integration**: Automatic error reporting with PII masking
- **Enhanced Error Handling**: Comprehensive error boundaries and recovery
- **Performance Improvements**: Optimized session state management
- **Security Features**: Consistent PII protection throughout
- **Debug Logging**: Conditional logging for development

### 5. Unified Loading Provider

**Location**: `@/features/ui/providers/loading-provider.tsx`

**Purpose**: Handles UI-specific loading states and optimistic UI, NOT authentication state.

**Responsibilities**:
- **Dashboard Loading States**: Skeleton timing and display logic
- **Optimistic UI**: Sign-out loading states and user feedback
- **UI Timing**: Minimum display times to prevent flashing
- **Performance Monitoring**: Sentry breadcrumbs for loading events

**Important**: Does NOT provide authentication state - components should use `useAuth` directly.

### 6. Enhanced Profile Services

**Location**: `@/features/user-auth-data/services/profile.service.ts`

**Advanced Optimizations**:
- **React Cache Integration**: Server-side caching with `cache()` function
- **Enhanced Error Logging**: Winston integration with structured logging
- **PII Masking**: Consistent user ID masking in all logs
- **Performance Monitoring**: Detailed operation tracking

## 📊 Component Data Source Patterns

### ✅ Advanced Pattern (Current Implementation)

All components now follow this consistent pattern with enhanced capabilities:

```typescript
// ✅ CORRECT: Single source of truth with advanced features
const { 
  user, 
  profile, 
  isAuthenticated, 
  isLoading,
  retry,
  clearErrors,
  _performance 
} = useAuth();

// ✅ For dashboard-specific optimistic UI only
const { isSigningOut, setIsSigningOut } = useDashboardLoading();

// ✅ Error recovery
if (profileError) {
  return (
    <ErrorBoundary onRetry={retry} onClear={clearErrors}>
      <ErrorFallback />
    </ErrorBoundary>
  );
}
```

### ❌ Old Pattern (Eliminated)

```typescript
// ❌ INCORRECT: Mixed data sources (eliminated)
const { user, profile } = useAuth();
const { isLoading: showSkeletons, isAuthenticated } = useDashboardLoading(); // Hardcoded values
```

## 🎯 Component Implementation Status

| **Component** | **Auth Data** | **Loading Data** | **Error Recovery** | **Performance** | **Status** |
|---------------|---------------|------------------|-------------------|-----------------|------------|
| **Homepage Hero Header** | `useAuth()` | `useAuth()` | `retry()` | `_performance` | ✅ **Enhanced** |
| **Dashboard User Menu** | `useAuth()` | `useAuth()` + optimistic UI | `retry()` | `_performance` | ✅ **Enhanced** |
| **Dashboard Homepage View** | `useAuth()` | `useAuth()` | `retry()` | `_performance` | ✅ **Enhanced** |
| **Profile View** | `useAuth()` | `useAuth()` | `retry()` | `_performance` | ✅ **Enhanced** |
| **Auth Guard** | `useAuth()` | `useAuth()` | `retry()` | `_performance` | ✅ **Enhanced** |
| **Dashboard Sidebar** | N/A | `useDashboardLoading()` | N/A | N/A | ✅ **Appropriate** |

## 🔄 Advanced Data Flow Architecture

### Authentication State Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Supabase      │    │   Enhanced       │    │   Components    │
│   Auth Client   │───▶│   useAuth Hook   │───▶│                 │
│                 │    │                  │    │ • Hero Header   │
│ • Session mgmt  │    │ • Session data   │    │ • User Menu     │
│ • User object   │    │ • Profile data   │    │ • Profile View  │
│ • Auth events   │    │ • Auth state     │    │ • Dashboard     │
│                 │    │ • Performance    │    │ • Auth Guard    │
│                 │    │ • Error recovery │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ Performance      │
                    │ Monitoring       │
                    │ • Render counts  │
                    │ • Fetch times    │
                    │ • Error rates    │
                    └──────────────────┘
```

### Server-Side Auth Flow (DRY Architecture)

```
┌─────────────────┐    ┌─────────────────────────┐    ┌─────────────────┐
│   Layout        │    │   Centralized Server    │    │   Cached        │
│   Components    │───▶│   Auth Utility          │───▶│   Services      │
│                 │    │                         │    │                 │
│ • Dashboard     │    │ • getServerAuthWith     │    │ • getServerAuth │
│ • Root Page     │    │   ProfilePrefetch()     │    │   State()       │
│ • Any Layout    │    │ • DRY implementation    │    │ • getCurrent    │
│                 │    │ • Configurable options  │    │   UserProfile() │
│                 │    │ • Consistent behavior   │    │ • All cached    │
└─────────────────┘    └─────────────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ Hydration        │
                    │ Boundary         │
                    │ • Dehydrated     │
                    │   state          │
                    │ • Client-side    │
                    │   hydration      │
                    └──────────────────┘
```

### Advanced Loading State Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   useAuth       │    │ Loading Provider │    │  UI Components  │
│                 │    │                  │    │                 │
│ • Auth loading  │    │ • UI timing      │    │ • Skeletons     │
│ • Session state │    │ • Optimistic UI  │    │ • Spinners      │
│ • Profile state │    │ • Dashboard UX   │    │ • Feedback      │
│ • Performance   │    │ • Error recovery │    │ • Retry UI      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Components Use:       │
                    │                           │
                    │ • useAuth (auth state)    │
                    │ • Loading (UI timing)     │
                    │ • retry() (error recovery)│
                    │ • _performance (debug)    │
                    └───────────────────────────┘
```

## 🎨 Advanced Skeleton Loading Implementation

### Profile View Skeleton

**Location**: `@/features/dashboard/profile/profile-view.tsx`

**Enhanced Loading Condition**:
```typescript
if (isSessionLoading || isLoadingAuth && !originalProfile && !profileError) {
  // Show comprehensive skeleton with performance context
  return <ProfileViewSkeleton performance={_performance} />;
}
```

**Features**:
- **Responsive Design**: Adapts to different screen sizes
- **Pulse Animation**: Smooth breathing effect with `animate-pulse`
- **Layout Matching**: Skeleton exactly matches the real form layout
- **Smart Conditions**: Shows only when appropriate, avoids unnecessary displays
- **Performance Context**: Debug information in development mode

### Hero Header Skeleton

**Location**: `@/features/homepage/components/hero-header/hero-header.tsx`

**Integration**: Uses `isLoading` from `useAuth` for consistent skeleton timing across all components with performance monitoring.

## 🛡️ Advanced Security & Error Handling

### Enhanced PII Masking

All components consistently mask sensitive information with additional context:

```typescript
// User IDs in logs appear as: "5d99e3..."
// Email addresses are masked in error reports
// Profile data is sanitized before logging
// Performance metrics included in error context
```

### Advanced Error Boundaries

- **Global Error Boundary**: Application-wide error recovery with performance context
- **Auth Error Boundary**: Feature-specific authentication error handling with retry mechanisms
- **Component Error Boundaries**: Granular error isolation with user-initiated recovery

### Enhanced Sentry Integration

- **Automatic Error Reporting**: All authentication errors reported to Sentry with performance context
- **PII Protection**: Sensitive data masked before transmission
- **Error Classification**: Proper error levels and categorization with retry attempt tracking
- **Performance Monitoring**: Track authentication flow performance with detailed metrics
- **Connection State**: Include online/offline status in error reports

## 📈 Advanced Performance Optimizations

### React Cache Integration

```typescript
// Server-side profile caching with enhanced error handling
export const getCurrentUserProfile = cache(async (userId: string) => {
  // Cached profile fetching logic with performance tracking
});

// Centralized server auth utility (DRY architecture)
export const getServerAuthWithProfilePrefetch = cache(async (options) => {
  // Uses existing cached services - no duplication!
  const { user, error } = await getServerAuthState(); // Already cached
  // Prefetch using existing cached profile service
  queryClient.prefetchQuery({
    queryFn: () => getCurrentUserProfile(userId), // Already cached
  });
});
```

### Advanced Memoization

```typescript
// Optimized hook with enhanced memoization and performance tracking
const authState = useMemo(() => ({
  user,
  profile,
  isAuthenticated,
  isLoading,
  _performance: debugAuth ? performanceRef.current : undefined,
  retry,
  clearErrors
}), [
  sessionUser?.id,
  profileData?.id,
  isSessionLoading,
  isProfileLoading,
  sessionError?.message,
  profileError?.message,
  debugAuth,
  retry,
  clearErrors
]);
```

### Connection Resilience

```typescript
// Offline-first networking with automatic retry
networkMode: 'offlineFirst',
refetchOnReconnect: true,
retry: (failureCount, error) => {
  if (!isOnlineRef.current) return false;
  return failureCount < 3;
}
```

### Background Sync

```typescript
// Automatic data refresh every 5 minutes
useEffect(() => {
  if (!backgroundSync || !userId || !data || isError) return;
  
  const syncInterval = setInterval(() => {
    if (isOnlineRef.current && !isFetching) {
      queryClient.invalidateQueries({
        queryKey: ['userProfile', userId],
        exact: true
      });
    }
  }, 5 * 60 * 1000);
  
  return () => clearInterval(syncInterval);
}, [backgroundSync, userId, data, isError, isFetching, queryClient]);
```

## 🔧 Advanced Development Guidelines

### For New Components

1. **Always use `useAuth` directly** for authentication state with error recovery:
   ```typescript
   const { 
     user, 
     profile, 
     isAuthenticated, 
     isLoading,
     retry,
     clearErrors 
   } = useAuth();
   ```

2. **Use loading provider only for UI states**:
   ```typescript
   const { isSigningOut } = useDashboardLoading(); // Only for optimistic UI
   ```

3. **Implement error recovery**:
   ```typescript
   if (profileError) {
     return (
       <ErrorBoundary onRetry={retry} onClear={clearErrors}>
         <ErrorFallback />
       </ErrorBoundary>
     );
   }
   ```

4. **Add performance monitoring** (development only):
   ```typescript
   if (process.env.NODE_ENV === 'development' && _performance) {
     console.log('Component performance:', _performance);
   }
   ```

### For New Layouts

1. **Use centralized server auth utility** (DRY principle):
   ```typescript
   const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
     prefetchProfile: true,
     profileTimeout: 500,
     requestId: `layout-${Date.now()}`,
     debugMode: shouldDebugServer
   });
   ```

2. **Consistent error handling**:
   ```typescript
   if (error) {
     logger.error('Layout auth error', { error: error.message, requestId });
     // Continue without user context if auth fails
   }
   ```

3. **Standard hydration pattern**:
   ```typescript
   return (
     <HydrationBoundary state={dehydratedState}>
       {children}
     </HydrationBoundary>
   );
   ```

### For Authentication Actions

Use optimized server actions with enhanced error handling:
```typescript
import { signInWithPassword, signOutAction } from '@/features/auth/actions';
```

### For Profile Updates

Use enhanced profile actions with performance tracking:
```typescript
import { updateUserProfile } from '@/features/user-auth-data/actions';
```

## 🎯 Advanced Migration Benefits

### Before Optimization

- **Mixed Data Sources**: Components got auth state from different providers
- **Inconsistent Loading**: Hardcoded loading values caused UI issues
- **Performance Issues**: No caching, inefficient data fetching
- **Limited Error Handling**: Basic error management without recovery
- **No Monitoring**: No performance tracking or debugging tools
- **Code Duplication**: Auth logic repeated across layouts

### After Advanced Optimization

- **Single Source of Truth**: All components use `useAuth` consistently with advanced features
- **DRY Architecture**: Centralized server auth utility eliminates code duplication
- **Consistent UI States**: Unified loading and skeleton behavior with error recovery
- **Enhanced Performance**: React cache, memoization, efficient prefetching, background sync
- **Advanced Error Handling**: Winston/Sentry integration with PII masking and retry mechanisms
- **Performance Monitoring**: Real-time tracking of render counts, fetch times, and performance metrics
- **Connection Resilience**: Offline-first networking with automatic retry and background sync
- **Better Developer Experience**: Clear patterns, debugging tools, and performance insights

## 🚀 Production Readiness

The advanced authentication system is production-ready with:

- ✅ **Consistent Data Flow**: All components use the same enhanced patterns
- ✅ **DRY Architecture**: Centralized utilities prevent code duplication
- ✅ **Performance Optimized**: Caching, memoization, efficient loading, background sync
- ✅ **Security Enhanced**: PII masking and comprehensive error handling
- ✅ **Connection Resilient**: Offline-first networking with automatic recovery
- ✅ **Performance Monitored**: Real-time metrics and debugging tools
- ✅ **Error Recovery**: User-initiated retry mechanisms with exponential backoff
- ✅ **Scalable Architecture**: Clear separation of concerns and maintainable code
- ✅ **Comprehensive Testing**: All components verified to work with enhanced flow

This architecture provides a robust, maintainable, performant, and resilient foundation for authentication and user data management throughout the application.

## 📚 Advanced Implementation Examples

### Hero Header Implementation

```typescript
// src/features/homepage/components/hero-header/hero-header.tsx
export function HeroHeader() {
  const { 
    user, 
    profile, 
    isAuthenticated, 
    isLoading,
    retry,
    _performance 
  } = useAuth(); // ✅ Single source with advanced features

  if (isLoading) {
    return <HeroHeaderSkeleton performance={_performance} />; // ✅ Enhanced loading
  }

  // Error recovery
  if (profileError) {
    return (
      <div>
        <p>Failed to load user data</p>
        <button onClick={retry}>Retry</button>
      </div>
    );
  }

  return (
    <header>
      {isAuthenticated ? (
        <div>Hi, {profile?.firstName || user?.email?.split('@')[0]}!</div>
      ) : (
        <div>Welcome! Please sign in.</div>
      )}
    </header>
  );
}
```

### Dashboard User Menu Implementation

```typescript
// src/features/dashboard/components/dashboard-user-menu.tsx
export function DashboardUserMenu() {
  const { 
    user, 
    profile, 
    isLoading,
    retry,
    clearErrors,
    _performance 
  } = useAuth(); // ✅ Auth state from useAuth
  const { isSigningOut, setIsSigningOut } = useDashboardLoading(); // ✅ Only optimistic UI

  const handleSignOut = async () => {
    setIsSigningOut(true); // Optimistic UI
    await signOutAction();
  };

  if (isLoading) {
    return <UserMenuSkeleton performance={_performance} />; // ✅ Enhanced skeleton
  }

  // Error recovery
  if (profileError) {
    return (
      <div>
        <p>Failed to load profile</p>
        <button onClick={retry}>Retry</button>
        <button onClick={clearErrors}>Clear</button>
      </div>
    );
  }

  return (
    <div>
      <Avatar src={profile?.avatarUrl} />
      <span>{profile?.firstName} {profile?.lastName}</span>
      <Button onClick={handleSignOut} disabled={isSigningOut}>
        {isSigningOut ? 'Signing out...' : 'Sign out'}
      </Button>
    </div>
  );
}
```

### Profile View Implementation

```typescript
// src/features/dashboard/profile/profile-view.tsx
export function ProfileView() {
  const {
    user,
    profile: originalProfile,
    isLoadingAuth,
    isSessionLoading,
    sessionError,
    profileError,
    retry,
    clearErrors,
    _performance
  } = useAuth(); // ✅ Comprehensive auth state

  // Smart skeleton condition with error recovery
  if (isSessionLoading || isLoadingAuth && !originalProfile && !profileError) {
    return <ProfileViewSkeleton performance={_performance} />; // ✅ Enhanced skeleton
  }

  if (sessionError || profileError) {
    return (
      <ErrorBoundary onRetry={retry} onClear={clearErrors}>
        <ErrorAlert error={sessionError || profileError} />
      </ErrorBoundary>
    );
  }

  return (
    <form>
      {/* Profile editing form */}
    </form>
  );
}
```

### Auth Guard Implementation

```typescript
// src/features/create-recipe/components/auth-guard.tsx
export function AuthGuard({ children, requireProfile = false }: AuthGuardProps) {
  const { 
    user, 
    profile, 
    isAuthenticated, 
    isLoading, 
    error,
    retry,
    clearErrors 
  } = useAuth();
  const { isLoading: isGlobalLoading } = useLoading();
  const router = useRouter();

  // Show loading state while checking authentication
  if (isLoading || isGlobalLoading) {
    return <AuthLoadingFallback />;
  }

  // Handle authentication errors with recovery
  if (error) {
    return (
      <ErrorBoundary onRetry={retry} onClear={clearErrors}>
        <UnauthorizedFallback />
      </ErrorBoundary>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated || !user) {
    return <UnauthorizedFallback />;
  }

  // Check if profile is required and available
  if (requireProfile && !profile) {
    return <ProfileIncompleteFallback />;
  }

  // User is authenticated and has required profile (if needed)
  return <>{children}</>;
}
```

### Layout Implementation (DRY Architecture)

```typescript
// src/app/(dashboard)/layout.tsx
export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // ✅ Use centralized server auth utility (DRY principle)
  const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
    prefetchProfile: true,
    profileTimeout: 500,
    profileStaleTime: 10 * 1000,
    requestId: `dashboard-${Date.now()}`,
    debugMode: shouldDebugServer
  });

  if (error) {
    logger.error('DashboardLayout: Auth error from centralized utility.', { 
      error: error.message,
      requestId 
    });
  }

  return (
    <DashboardLoadingProvider>
      <SidebarProvider initialVariant="inset" initialCollapsible="offcanvas">
        <DashboardAppSidebar />
        <SidebarInset className="bg-background">
          <DashboardHeader />
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  <HydrationBoundary state={dehydratedState}>
                    {children}
                  </HydrationBoundary>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </DashboardLoadingProvider>
  );
}
```

## 🔍 Advanced Debugging and Monitoring

### Client-Side Debugging

The enhanced `useAuth` hook provides comprehensive logging:

```typescript
// Console logs show:
[useAuth] State changed: authenticated, ready, ok {
  performance: { renderCount: 5, lastRenderTime: ********** },
  retryCount: 0,
  hasProfile: true
}
[useUserProfileQuery] State change for userId 5d99e3...: {
  status: 'success',
  isLoading: false,
  isSuccess: true,
  isError: false,
  isStale: false,
  isFetching: false,
  hasData: true,
  performance: {
    fetchCount: 3,
    averageFetchTime: 245,
    cacheHitCount: 2,
    backgroundSyncCount: 1
  }
}
```

### Server-Side Monitoring

Enhanced Winston logging with structured data:

```typescript
// Server logs show:
[ProfileService] info: Profile fetched successfully {
  "userId": "5d99e3...", // Masked
  "operation": "getCurrentUserProfile",
  "hasData": true,
  "duration": 245
}
```

### Advanced Sentry Error Tracking

Automatic error reporting with enhanced context:

```typescript
// Errors automatically reported to Sentry with:
// - Masked user IDs
// - Sanitized error messages
// - Performance metrics
// - Retry attempt tracking
// - Connection state (online/offline)
// - Proper error categorization
```

## 🧪 Advanced Testing Patterns

### Component Testing

```typescript
// Test components with mocked useAuth
const mockUseAuth = {
  user: mockUser,
  profile: mockProfile,
  isAuthenticated: true,
  isLoading: false,
  retry: jest.fn(),
  clearErrors: jest.fn(),
  _performance: { renderCount: 1, lastRenderTime: Date.now() }
};

jest.mock('@/features/auth/hooks', () => ({
  useAuth: () => mockUseAuth
}));
```

### Integration Testing

```typescript
// Test complete auth flow with error recovery
describe('Authentication Flow', () => {
  it('should show skeleton during loading', () => {
    mockUseAuth.isLoading = true;
    render(<Component />);
    expect(screen.getByTestId('skeleton')).toBeInTheDocument();
  });

  it('should show user data when loaded', () => {
    mockUseAuth.isLoading = false;
    render(<Component />);
    expect(screen.getByText('Hi, John!')).toBeInTheDocument();
  });

  it('should show retry button on error', () => {
    mockUseAuth.profileError = new Error('Failed to load');
    render(<Component />);
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should call retry function when retry button clicked', () => {
    mockUseAuth.profileError = new Error('Failed to load');
    render(<Component />);
    fireEvent.click(screen.getByText('Retry'));
    expect(mockUseAuth.retry).toHaveBeenCalled();
  });
});
```

### Performance Testing

```typescript
// Test performance monitoring
describe('Performance Monitoring', () => {
  it('should track render counts', () => {
    render(<Component />);
    expect(mockUseAuth._performance.renderCount).toBeGreaterThan(0);
  });

  it('should track fetch times', () => {
    const { _performance } = useUserProfileQuery(userId);
    expect(_performance.averageFetchTime).toBeGreaterThan(0);
  });
});
```

## 🔄 Advanced Migration Checklist

When updating existing components to use the enhanced pattern:

- [ ] **Replace mixed data sources** with `useAuth()` only
- [ ] **Remove hardcoded loading states** from loading providers
- [ ] **Update skeleton conditions** to use `isLoading` from `useAuth`
- [ ] **Implement error recovery** with `retry()` and `clearErrors()`
- [ ] **Add performance monitoring** with `_performance` in debug mode
- [ ] **Update error boundaries** to use enhanced error handling
- [ ] **Add PII masking** to any logging statements
- [ ] **Test loading states** and skeleton animations
- [ ] **Test error recovery** mechanisms
- [ ] **Verify performance metrics** in development mode
- [ ] **Test connection resilience** with offline/online scenarios
- [ ] **Validate background sync** functionality
- [ ] **Check Sentry integration** with enhanced context

When updating existing layouts to use DRY architecture:

- [ ] **Replace custom auth logic** with `getServerAuthWithProfilePrefetch()`
- [ ] **Remove duplicated caching code** (use existing cached services)
- [ ] **Standardize error handling** across all layouts
- [ ] **Add request tracking** with unique request IDs
- [ ] **Test auth flow** in all layout scenarios
- [ ] **Verify performance** improvements from centralized caching
- [ ] **Update documentation** to reflect DRY patterns

## 📖 Related Documentation

- **Authentication Actions**: See `src/features/auth/actions/README.md`
- **Profile Management**: See `src/features/user-auth-data/README.md`
- **Dashboard Components**: See `src/features/dashboard/README.md`
- **Error Logging Guidelines**: See `docs/error-logging-guidelines.md`
- **Loading Provider Usage**: See `src/features/ui/providers/README.md`
- **Server Auth Utilities**: See `src/lib/auth/server-auth.utils.ts`

## 🎯 Future Enhancements

### Planned Optimizations

1. **Enhanced Caching**: Implement more granular cache invalidation
2. **Offline Support**: Add offline authentication state management
3. **Performance Metrics**: Detailed authentication flow analytics
4. **Advanced Error Recovery**: Automatic retry mechanisms
5. **Enhanced Security**: Additional PII protection measures

### Monitoring Improvements

1. **Real-time Dashboards**: Authentication flow monitoring
2. **Performance Alerts**: Slow authentication detection
3. **Error Analytics**: Detailed error pattern analysis
4. **User Experience Metrics**: Loading time optimization

This comprehensive architecture ensures consistent, performant, and secure authentication throughout the application while providing excellent developer experience and maintainability.
