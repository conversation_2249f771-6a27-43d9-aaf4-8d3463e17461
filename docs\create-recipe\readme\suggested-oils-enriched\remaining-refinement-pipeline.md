# Remaining Oil Refinement Pipeline

**Objective:** This document outlines the implementation plan for the remaining data refinement strategies. The goal is to enhance the essential oil suggestions with critical safety filtering and backend performance optimizations.

---

## 1. Demographic Safety Filtering

**Objective:** Ensure user safety by pro-actively filtering the suggested oils list based on the user's demographic data (e.g., age). This logic should be implemented in the frontend, within the refinement pipeline located in `src/features/create-recipe/components/properties-display.tsx`.

### Implementation Steps

1.  **Access Demographics**: Within the refinement pipeline, retrieve the user's demographics from the Zustand store.
    ```typescript
    // In properties-display.tsx
    const { demographics } = useRecipeStore.getState();
    ```

2.  **Apply `filter()` Operation**: Add a new `.filter()` step to the array manipulation chain after the initial data quality mapping.

3.  **Define Safety Rules**: Implement filtering logic based on age categories.

    ```typescript
    // Example implementation within the pipeline
    
    // ... .map(...)
    // ... .filter(Boolean)
    .filter(oil => {
      // Rule for Children
      if (demographics.ageCategory === 'child') {
        if (oil.safety?.dermocaustic || oil.safety?.neurotoxic) {
          console.log(`🛡️ SAFETY FILTER: Removing "${oil.name_english}" (Dermocaustic/Neurotoxic) for child.`);
          return false;
        }
      }

      // Rule for Elderly
      if (demographics.ageCategory === 'elderly') {
        // Example: a certain oil might have contraindications for the elderly
        if (oil.safety?.contraindications?.includes('elderly')) {
           console.log(`🛡️ SAFETY FILTER: Removing "${oil.name_english}" due to elderly contraindications.`);
           return false;
        }
      }
      
      // Add other safety rules here (e.g., pregnancy if data becomes available)

      // If no rules match, keep the oil
      return true;
    })
    // ...
    ```

### Required Data

-   This step depends on the `oil.safety` object being present in the `EnrichedEssentialOil` type, which is populated during the `oil-enrichment` phase.
-   The frontend state must have `demographics.ageCategory`.

## 2. `is_ultra_safe` Flag

**Objective:** Create a boolean flag to easily identify essential oils that are exceptionally safe and suitable for beginners or sensitive users. This flag will be calculated and added to the oil object during the frontend refinement pipeline.

### Implementation Steps

1.  **Define Criteria**: Establish the business logic for what constitutes an "ultra safe" oil.
    *   **Proposal**: `is_ultra_safe = oil.safety.dilution_level === 'low' && oil.safety.photosensitive === false && oil.safety.internal_use.status === 'safe'`

2.  **Add to Mapping Logic**: Incorporate this logic into the `.map()` function of the refinement pipeline.

    ```typescript
    // Example implementation inside the .map() callback
    
    .map(enrichedOil => {
      if (enrichedOil.enrichment_status === 'discarded' || enrichedOil.enrichment_status === 'not_found') {
        return null;
      }

      // Calculate the is_ultra_safe flag
      const isUltraSafe = enrichedOil.safety?.dilution?.percentage_max <= 2 &&
                            !enrichedOil.safety?.phototoxicity?.status &&
                            enrichedOil.safety?.internal_use?.code === 'GRAS';
                            
      return {
        ...enrichedOil,
        isEnriched: true,
        is_ultra_safe: isUltraSafe,
        // ... other calculated fields like confidence_score
      };
    })
    ```

### UI Implications

-   The `is_ultra_safe` flag can be used in the `OilTableRow` component to display a visual indicator, such as a shield icon 🛡️ or a "Beginner Friendly" badge.
-   It can also be used to enable a user-facing filter option.

## 3. Backend Caching

**Objective:** Improve performance and reduce costs by implementing a simple caching mechanism in the `BatchEnrichmentService` on the backend. This will prevent repeated database and potential external API calls for frequently requested oils.

### Implementation Strategy (In-Memory Cache)

1.  **Create a Cache Store**: Instantiate an in-memory `Map` at the service level.

    ```typescript
    // In: src/lib/ai/services/batch-enrichment.service.ts
    
    const oilEnrichmentCache = new Map<string, EnrichedEssentialOil>();
    ```

2.  **Define a Cache Key**: Create a consistent key for each oil request. A combination of English and botanical names is robust.

    ```typescript
    function getCacheKey(nameEnglish: string, nameBotanical: string): string {
      return `${nameEnglish.toLowerCase().trim()}|${nameBotanical.toLowerCase().trim()}`;
    }
    ```

3.  **Implement Cache Logic**: Before processing an oil, check the cache. If a result exists, return it. Otherwise, process the oil and store the result in the cache before returning.

    ```typescript
    // Inside the oil processing loop in `batchEnrichOils`
    
    for (const oil of oilsToEnrich) {
      const cacheKey = getCacheKey(oil.name_english, oil.name_botanical);
      
      if (oilEnrichmentCache.has(cacheKey)) {
        const cachedOil = oilEnrichmentCache.get(cacheKey);
        // Ensure you return a copy to avoid unintended mutations
        enrichedResults.push({ ...cachedOil }); 
        continue;
      }

      // ... existing enrichment logic to process the oil ...
      
      const enrichedOil = // result from your Supabase/similarity search
      
      // Store the result in the cache
      oilEnrichmentCache.set(cacheKey, enrichedOil);
      enrichedResults.push(enrichedOil);
    }
    ```

### Considerations

-   **Cache Invalidation**: For an in-memory cache, the cache is cleared on server restart. For more advanced use cases, a TTL (Time-To-Live) could be added or a persistent cache like Redis could be used.
-   **Data Consistency**: This strategy assumes the underlying oil data in Supabase does not change frequently. If it does, a more complex cache-invalidation strategy would be needed. 