# Schema Change Guide: Create Recipe Feature

## Overview

The create-recipe feature uses **multiple streaming architectures** across different steps, each requiring specific files to be updated when schemas change. This guide provides a comprehensive checklist of all files that must be reviewed and updated when modifying AI output schemas.

## Architecture Summary

### Streaming Approaches by Step

| Step | Component | Streaming Hook | Schema File | JSON Path | Response Parser |
|------|-----------|----------------|-------------|-----------|-----------------|
| **Demographics** | `demographics-form.tsx` | `useAIStreaming` | `potential-causes.yaml` | `data.potential_causes` | `extractFinalStreamingData()` |
| **Causes** | `causes-selection.tsx` | `useAIStreaming` | `potential-symptoms.yaml` | `data.potential_symptoms` | `extractFinalStreamingData()` |
| **Symptoms** | `symptoms-selection.tsx` | `useAIStreaming` | `therapeutic-properties.yaml` | `data.therapeutic_properties` | `extractFinalStreamingData()` |
| **Oil Suggestions** | `properties-display.tsx` | `useCreateRecipeStreaming` → `useParallelStreamingEngine` | `suggested-oils.yaml` | N/A | `updates.finalData?.data?.suggested_oils` |
| **Oil Enrichment** | `therapeutic-properties-table.tsx` | `useCreateRecipeStreaming` → `useParallelStreamingEngine` | `oil-enrichment.yaml` | N/A | `updates.finalData?.data?.enriched_oils` ⚠️ **UPDATED** |

### Data Flow Architecture

```
YAML Schema → AI Response → Response Parser → Zustand Store → React Components → User UI
```

Each layer must understand the same data structure, or the chain breaks.

---

## 🚀 QUICK START - CRITICAL STEPS ONLY

**If you're in a hurry, these are the ONLY 3 steps that MUST be done correctly for most schema changes:**

### Step 1: Update YAML Schema ⚠️ **CRITICAL**
```bash
# File: src/features/create-recipe/prompts/[your-step].yaml
# Update the schema section to match your desired AI output structure
```

### Step 2: Update TypeScript Types ⚠️ **CRITICAL** 
```bash
# File: src/features/create-recipe/types/recipe.types.ts
# Add/update interfaces to match your YAML schema EXACTLY
```

### Step 3: Update Response Parser ⚠️ **#1 FAILURE POINT**
```bash
# File: src/features/create-recipe/hooks/use-create-recipe-streaming.ts
# Update the responseParser path to match your new schema structure

# COMMON PATHS BY STEP:
# Oil Suggestions: updates.finalData?.data?.suggested_oils
# Oil Enrichment: updates.finalData?.data?.enriched_oils  ← CORRECTED PATH
# Other steps use different hooks (see full guide below)
```

### Quick Validation
```bash
# Test TypeScript compilation
npx tsc --noEmit

# Test your changes and look for these SUCCESS indicators:
# ✅ Console: "[ResponseParser] Found [your_data]: X items"
# ✅ Console: "[ParallelEngine] Parsed result for ID: SUCCESS" 
# ❌ NEVER: "NO_RESULT" errors
```

**⚡ If these 3 steps work, you're 90% done. See full guide below for edge cases.**

---

## Complete File Checklist

### 1. PROMPT SCHEMA DEFINITION LAYER ⚠️ **CRITICAL**

**Files that define the expected AI output structure:**

- `src/features/create-recipe/prompts/potential-causes.yaml`
- `src/features/create-recipe/prompts/potential-symptoms.yaml` 
- `src/features/create-recipe/prompts/therapeutic-properties.yaml`
- `src/features/create-recipe/prompts/suggested-oils.yaml`
- `src/features/create-recipe/prompts/oil-enrichment.yaml`

**Impact:** These files contain the JSON schema that defines exactly what structure the AI should return. Changes here require corresponding changes in ALL downstream files.

### 2. TYPE DEFINITION LAYER ⚠️ **CRITICAL**

**Files that define TypeScript interfaces matching the AI responses:**

- `src/features/create-recipe/types/recipe.types.ts` - **MUST UPDATE**
  - Contains interfaces like `TherapeuticProperty`, `EssentialOil`, `EnrichedEssentialOil`, `OilSafetyInfo`
  - Must match YAML schema structure exactly

- `src/features/create-recipe/schemas/recipe-schemas.ts`
  - Contains Zod validation schemas
  - Must match both YAML and TypeScript types

**Impact:** These must be updated to match new schema fields/structure or TypeScript compilation will fail.

### 3. API REQUEST TRANSFORMATION LAYER ⚠️ **CRITICAL**

**Files that prepare data for AI requests:**

- `src/features/create-recipe/utils/api-data-transform.ts` - **MUST UPDATE**
  - `createStreamRequest()` function formats request data
  - Must match template variables in YAML prompts
  - Handles special cases like oil-enrichment data structure

**Impact:** If YAML template variables change, this function must be updated to provide the correct data structure.

### 4. CORE STREAMING INFRASTRUCTURE LAYER ⚠️ **CRITICAL**

**Files that handle different streaming patterns:**

- `src/lib/ai/hooks/use-ai-streaming.ts` - **MUST UPDATE**
  - Used by: Demographics, Causes, Symptoms steps
  - Handles single-stream responses with `jsonArrayPath` configuration
  - Contains response parsing logic in `handleServiceUpdate()`
  - Must handle schema changes for structured data processing

- `src/lib/ai/hooks/use-parallel-streaming-engine.ts` - **MUST UPDATE**
  - Used by: Oil suggestions and Oil enrichment steps
  - Handles multiple parallel streams with `responseParser` functions
  - Contains generic streaming logic that processes multiple responses simultaneously

- `src/lib/ai/services/streaming.service.ts` - **MUST UPDATE**
  - Core service used by both streaming approaches
  - Handles SSE connections, JSON parsing, error handling
  - Must support different response formats from different steps

### 5. STEP-SPECIFIC STREAMING HOOKS ⚠️ **CRITICAL**

**Files that implement feature-specific streaming logic:**

- `src/features/create-recipe/hooks/use-create-recipe-streaming.ts` - **MUST UPDATE**
  - Contains `responseParser` functions for parallel streaming steps:
    - Suggested oils: `updates.finalData?.data?.suggested_oils` ⚠️ **CRITICAL PATH**
    - Oil enrichment: `updates.finalData?.data?.enriched_oils` ⚠️ **CORRECTED PATH**
  - **CRITICAL**: Must match the EXACT response structure from AI
  - **COMMON FAILURE**: Old legacy paths may still be hardcoded
  - **RECENT SUCCESS**: Oil enrichment path was simplified from `output.data` to just `data`

**Impact:** Response parsers must be updated to match new schema paths or data won't reach the frontend. **This is the #1 cause of "NO_RESULT" errors.**

**🔍 DEBUGGING TIP**: Always add logging to see the actual response structure:
```typescript
responseParser: (updates) => {
  console.log('🔍 [ResponseParser] Full structure:', JSON.stringify(updates.finalData, null, 2));
  // Then update your path based on what you see
}
```

### 6. RESPONSE PARSING UTILITIES ⚠️ **CRITICAL**

**Files that extract data from different response formats:**

- `src/lib/ai/utils/streaming-utils.ts` - **MUST UPDATE**
  - `extractFinalStreamingData()` function used by single-stream steps
  - `processStreamingText()` and `processStructuredData()` functions
  - `transformRecipeWizardData()` function for data transformation
  - Must handle different schema structures from different steps

### 7. STEP-SPECIFIC COMPONENT LAYER ⚠️ **CRITICAL**

**Files that use different streaming hooks:**

- `src/features/create-recipe/components/demographics-form.tsx` - **MUST UPDATE**
  - Uses `useAIStreaming` with `jsonArrayPath: 'data.potential_causes'`
  - Response parsing: `extractFinalStreamingData(finalData, 'potential_causes')`
  - Must match `potential-causes.yaml` schema

- `src/features/create-recipe/components/causes-selection.tsx` - **MUST UPDATE**
  - Uses `useAIStreaming` with `jsonArrayPath: 'data.potential_symptoms'`
  - Response parsing: `extractFinalStreamingData(symptomsFinalData, 'data.potential_symptoms')`
  - Must match `potential-symptoms.yaml` schema

- `src/features/create-recipe/components/symptoms-selection.tsx` - **MUST UPDATE**
  - Uses TWO `useAIStreaming` hooks:
    1. Symptoms: `jsonArrayPath: 'data.potential_symptoms'`
    2. Properties: `jsonArrayPath: 'data.therapeutic_properties'`
  - Must match both `potential-symptoms.yaml` and `therapeutic-properties.yaml` schemas

- `src/features/create-recipe/components/therapeutic-properties-table.tsx` - **CHECK**
  - Displays therapeutic properties and essential oils
  - References fields like `property_name`, `description`, `suggested_oils`
  - Handles oil enrichment data display

- `src/features/create-recipe/components/properties-display.tsx` - **CHECK**
  - Main container for properties step
  - Manages streaming state and user interactions

### 8. API ROUTE HANDLER LAYER ⚠️ **CRITICAL - OFTEN MISSED**

**Files that process streaming requests:**

- `src/app/api/ai/streaming/route.ts` - **MUST UPDATE** ⚠️
  - **CRITICAL**: Contains special handling for different data types
  - **LEGACY TRAP**: May have old schema-specific code that needs updating
  - Look for hardcoded paths like `property_oil_suggestion`, `property_oil_suggestions`
  - Must match the actual schema structure in YAML files
  - Handles structured output and tool integration

- `src/lib/ai/utils/prompt-manager.ts` - **CHECK**
  - Loads and processes YAML prompt files
  - Handles template variable substitution

### 9. STATE MANAGEMENT LAYER

**Files that manage application state:**

- `src/features/create-recipe/store/recipe-store.ts` - **CHECK**
  - Contains state interfaces and update functions
  - Must handle the data structures returned by the streaming hooks
  - Functions like `updateTherapeuticProperties()`, `updateSuggestedOils()`

### 10. UI DISPLAY LAYER

**Files that display the data to users:**

- `src/features/create-recipe/components/oil-safety-display.tsx` - **CHECK**
  - Displays safety information for enriched oils
  - Must match `OilSafetyInfo` interface structure

### 11. VALIDATION AND ERROR HANDLING

**Files that validate data integrity:**

- `src/lib/ai/types/streaming-data-types.ts` - **CHECK**
  - Contains validation functions for streaming data
  - `isItemComplete()`, `cleanItemData()` functions

### 12. UTILITY AND HELPER LAYER

**Files that process or transform the data:**

- `src/features/create-recipe/utils/index.ts` - **CHECK**
  - Contains helper functions like `isEnrichedOil()`

---

## 🎯 SUCCESSFUL CASE STUDY: Oil Enrichment Schema Migration

**This section shows exactly what a successful schema migration looks like, including console output.**

### What We Changed
1. **YAML Schema**: Updated `oil-enrichment.yaml` from simple structure to standardized `meta`, `data`, `echo` structure
2. **TypeScript Types**: Added `OilEnrichmentMeta`, `TherapeuticPropertyContext`, `OilEnrichmentEcho`, `OilEnrichmentResponse` interfaces
3. **Response Parser**: Changed path from `updates.finalData?.output?.data?.enriched_oils` to `updates.finalData?.data?.enriched_oils`

### Console Output - What SUCCESS Looks Like
```bash
# ✅ SUCCESSFUL CONSOLE OUTPUT:
[Oil Enrichment] Starting oil enrichment for properties: 1
[ParallelEngine] Starting stream for ID: 3f50e8a7-1b2c-4d9f-8e7a-9b8e2a1f2b3c
[Oil Enrichment] Response parser received updates for: Calmante
[Oil Enrichment] Successfully parsed enriched oils: 8 oils  ← KEY SUCCESS INDICATOR
[Oil Enrichment] Therapeutic property context found: 3f50e8a7-1b2c-4d9f-8e7a-9b8e2a1f2b3c
[ParallelEngine] Parsed result for 3f50e8a7-1b2c-4d9f-8e7a-9b8e2a1f2b3c: SUCCESS  ← KEY SUCCESS INDICATOR
[ParallelEngine] All streams completed. Results: 1/1
[Oil Enrichment] Final enrichment results (Map): Map(1) {'...' => Array(8)}
[recipe-store] Found 1 enriched properties
[Enrichment] Successfully enriched property: Calmante  ← UI SUCCESS
```

### Key Success Indicators
- ✅ `Successfully parsed enriched oils: X oils`
- ✅ `Parsed result for ID: SUCCESS` (NOT `NO_RESULT`)
- ✅ `All streams completed. Results: X/X`
- ✅ Final UI shows enriched data

### What the New Schema Structure Looks Like
```json
{
  "meta": {
    "step_name": "oil_enrichment",
    "request_id": "61f3e7d9-8ef0-4ec5-b3ee-191287d2daa4",
    "timestamp_utc": "2025-05-19T12:00:00Z",
    "status": "success"
  },
  "data": {
    "therapeutic_property_context": {
      "property_id": "3f50e8a7-1b2c-4d9f-8e7a-9b8e2a1f2b3c",
      "property_name_localized": "Calmante"
    },
    "enriched_oils": [
      {
        "oil_id": "1f2e3d4c-5b6a-7c8d-9e0f-1a2b3c4d5e6f",
        "name_english": "Lavender",
        "isEnriched": true,
        "safety": { /* safety data */ }
      }
    ]
  },
  "echo": { /* input data echo */ }
}
```

**💡 LESSON LEARNED**: The response parser path was simpler than expected. Always log the actual structure first!

---

## 🚨 CRITICAL DEBUGGING PROCESS

**MANDATORY**: Before making any schema change, follow these debugging steps to identify all affected files:

### Step 1: Enable Comprehensive Logging

**Add detailed logging to response parsers to see exactly what data structure is received:**

```typescript
// In use-create-recipe-streaming.ts responseParser
responseParser: (updates) => {
  console.log(`🔍 [ResponseParser] Processing updates for property ${property.property_name}:`, {
    hasFinalData: !!updates.finalData,
    finalDataKeys: updates.finalData ? Object.keys(updates.finalData) : [],
    fullFinalData: updates.finalData
  });
  
  if (updates.finalData) {
    console.log(`🔍 [ResponseParser] Final data structure:`, JSON.stringify(updates.finalData, null, 2));
    // ... rest of parsing logic
  }
}
```

### Step 2: Search for Legacy Schema References

**Run these searches to find ALL files that reference old schema names:**

```bash
# Search for old response paths
grep -r "property_oil_suggestion" src/ --include="*.ts" --include="*.tsx"
grep -r "property_oil_suggestions" src/ --include="*.ts" --include="*.tsx"

# Search for other legacy field names (update for your schema)
grep -r "old_field_name" src/ --include="*.ts" --include="*.tsx"
```

### Step 3: Identify ALL Response Parser Paths

**Check these files for hardcoded response paths:**

1. `src/features/create-recipe/hooks/use-create-recipe-streaming.ts`
2. `src/features/create-recipe/hooks/use-create-recipe-streaming-fixed.ts` (if exists)
3. `src/app/api/ai/streaming/route.ts` ⚠️ **MOST COMMONLY MISSED**
4. `src/lib/debug/streaming-logger.ts`
5. Any other `*-streaming.ts` files

### Step 4: Verify TypeScript Interface Alignment

**Ensure your TypeScript interfaces EXACTLY match your YAML schema:**

```yaml
# YAML Schema Structure
data:
  therapeutic_property_context:
    property_id: string
    property_name_localized: string
  suggested_oils:
    - oil_id: string
      name_english: string
```

```typescript
// TypeScript Interface MUST match exactly
interface SuggestedOilsResponse {
  therapeutic_property_context: {
    property_id: string;
    property_name_localized: string;
    // ... other fields
  };
  suggested_oils: EssentialOil[];
}
```

### Step 5: Test End-to-End Data Flow

**Verify data flows correctly through each layer:**

1. **YAML Schema** → AI generates response matching schema
2. **API Route Handler** → Processes response correctly
3. **Streaming Service** → Forwards structured data
4. **Response Parser** → Extracts data using correct paths
5. **Component** → Receives and displays data

**Look for these console messages:**
- ✅ `[ResponseParser] Found suggested_oils: X oils`
- ❌ `[ParallelEngine] Parsed result for ID: NO_RESULT`
- ❌ `[ResponseParser] No data object found`

---

## Common Failure Patterns & Solutions

### 🚨 Pattern 1: "NO_RESULT" Errors
**Symptom:** `[ParallelEngine] Parsed result for ID: NO_RESULT`
**Root Cause:** Response parser path doesn't match actual AI response structure
**Solution:** Add logging to see actual response structure, update parser paths

### 🚨 Pattern 2: API Route Handler Legacy Code
**Symptom:** Data not reaching streaming service
**Root Cause:** API route has hardcoded handling for old schema names
**Solution:** Check `src/app/api/ai/streaming/route.ts` for special case handling

### 🚨 Pattern 3: TypeScript Interface Mismatch
**Symptom:** TypeScript compilation errors or runtime type issues
**Root Cause:** Interface doesn't match YAML schema structure
**Solution:** Update interfaces to match YAML schema exactly

### 🚨 Pattern 4: Template Variable Mismatch
**Symptom:** AI receives wrong data structure
**Root Cause:** `createStreamRequest()` doesn't provide data that matches YAML template variables
**Solution:** Update `api-data-transform.ts` to match YAML template needs

---

## Schema Change Process

### Phase 1: Core Data Structures ⚠️ **CRITICAL**

1. **Update YAML Schema File**
   - Modify the appropriate `.yaml` file in `src/features/create-recipe/prompts/`
   - Ensure JSON schema is valid and complete

2. **Update TypeScript Types**
   - Update `src/features/create-recipe/types/recipe.types.ts` interfaces
   - Update `src/features/create-recipe/schemas/recipe-schemas.ts` Zod schemas

3. **Update Request Transformation**
   - Update template variables in `src/features/create-recipe/utils/api-data-transform.ts`
   - Ensure `createStreamRequest()` provides correct data structure

### Phase 2: Response Processing ⚠️ **CRITICAL**

**For Single-Stream Steps (Demographics, Causes, Symptoms):**

4. **Check Core Streaming Hook**
   - Verify `src/lib/ai/hooks/use-ai-streaming.ts` handles new structure
   - Ensure `jsonArrayPath` configuration matches new schema

5. **Update Response Utilities**
   - Check `src/lib/ai/utils/streaming-utils.ts`
   - Ensure `extractFinalStreamingData()` handles new paths
   - Update `transformRecipeWizardData()` if needed

6. **Update Component Response Processing**
   - Update the specific component files based on which schema changed:
     - `demographics-form.tsx` for potential-causes schema
     - `causes-selection.tsx` for potential-symptoms schema  
     - `symptoms-selection.tsx` for symptoms and properties schemas

**For Parallel-Stream Steps (Oil suggestions, Oil enrichment):**

7. **Update Parallel Streaming Engine**
   - Check `src/lib/ai/hooks/use-parallel-streaming-engine.ts`
   - Ensure generic processing works with new structure

8. **Update Response Parsers**
   - Update `src/features/create-recipe/hooks/use-create-recipe-streaming.ts`
   - Update `responseParser` functions:
     - `updates.finalData?.data?.property_oil_suggestion` for suggested-oils
     - `updates.finalData?.output?.data?.enriched_oils` for oil-enrichment

### Phase 3: Infrastructure Verification

9. **Verify Core Services**
   - Check `src/lib/ai/services/streaming.service.ts` handles new response formats
   - Verify `src/app/api/ai/streaming/route.ts` processes new schemas correctly

### Phase 4: State Management & UI

10. **Update State Management**
    - Update `src/features/create-recipe/store/recipe-store.ts` state management
    - Ensure state update functions handle new fields

11. **Update Display Components**
    - Update UI components to show new fields
    - Update utility functions that process the data
    - Update validation functions

### Phase 5: Testing & Validation

12. **End-to-End Testing**
    - Test complete data flow from AI response to UI display
    - Verify TypeScript compilation passes
    - Test error handling for malformed responses
    - Test each streaming approach individually

## ⚡ SIMPLIFIED TESTING GUIDE

**Based on our successful migration, here are the EXACT commands and checks that work:**

### Quick Validation Commands
```bash
# 1. TypeScript compilation check (REQUIRED)
npx tsc --noEmit
# Should show no errors related to your changes

# 2. YAML syntax validation (OPTIONAL but recommended)
node -e "const fs = require('fs'); const yaml = require('yaml'); try { const content = fs.readFileSync('src/features/create-recipe/prompts/[your-file].yaml', 'utf8'); yaml.parse(content); console.log('✅ YAML syntax is valid'); } catch(e) { console.log('❌ YAML error:', e.message); }"
```

### Success Indicators During Testing
```bash
# ✅ WHAT TO LOOK FOR IN BROWSER CONSOLE:
"[ResponseParser] Successfully parsed [your_data]: X items"
"[ParallelEngine] Parsed result for ID: SUCCESS"
"All streams completed. Results: X/X"
"Successfully enriched/processed [your_step]"

# ❌ FAILURE INDICATORS (IMMEDIATE RED FLAGS):
"[ParallelEngine] Parsed result for ID: NO_RESULT"  ← #1 FAILURE INDICATOR
"No data object found"
"Cannot read property of undefined"
```

### Development Testing Flow
1. **Make your 3 critical changes** (YAML, Types, Response Parser)
2. **Run TypeScript check**: `npx tsc --noEmit`
3. **Start development server**: `npm run dev`
4. **Test the specific step** that uses your changed schema
5. **Watch browser console** for success/failure indicators above
6. **If you see `NO_RESULT`**: Add logging to your response parser and check the actual structure

### Real-World Testing Example
```typescript
// Add this to your responseParser for debugging:
responseParser: (updates) => {
  console.log('🔍 [DEBUG] Full response structure:', JSON.stringify(updates.finalData, null, 2));
  
  // Your actual parsing logic here
  if (updates.finalData?.data?.your_data_field) {
    console.log('✅ [SUCCESS] Found your_data_field:', updates.finalData.data.your_data_field.length);
    return updates.finalData.data.your_data_field;
  }
  
  console.log('❌ [FAILURE] Could not find your_data_field in response');
  return null;
}
```

**💡 PRO TIP**: If it works in console and displays in UI, your migration is complete!

---

## Critical Checkpoints by Schema Type

### Potential Causes Schema Changes
**Files to prioritize:**
- `potential-causes.yaml` → `demographics-form.tsx` → `use-ai-streaming.ts` → `streaming-utils.ts`

### Potential Symptoms Schema Changes  
**Files to prioritize:**
- `potential-symptoms.yaml` → `causes-selection.tsx` + `symptoms-selection.tsx` → `use-ai-streaming.ts`

### Therapeutic Properties Schema Changes
**Files to prioritize:**
- `therapeutic-properties.yaml` → `symptoms-selection.tsx` → `use-ai-streaming.ts` → `recipe.types.ts`

### Suggested Oils Schema Changes
**Files to prioritize:**
- `suggested-oils.yaml` → `use-create-recipe-streaming.ts` → `use-parallel-streaming-engine.ts` → `properties-display.tsx`

### Oil Enrichment Schema Changes
**Files to prioritize:**
- `oil-enrichment.yaml` → `use-create-recipe-streaming.ts` → `therapeutic-properties-table.tsx` → `recipe.types.ts`

---

## Common Pitfalls

### 1. Mismatched JSON Paths
- Single-stream steps use `jsonArrayPath` in `useAIStreaming`
- Parallel-stream steps use custom `responseParser` functions
- Ensure paths match the actual AI response structure

### 2. Type Definition Mismatches
- TypeScript interfaces must exactly match YAML schema
- Zod schemas must validate the same structure
- Missing fields will cause runtime errors

### 3. Response Parser Path Errors
- Oil suggestions: `updates.finalData?.data?.property_oil_suggestion`
- Oil enrichment: `updates.finalData?.output?.data?.enriched_oils`
- Different steps may have different response structures

### 4. Template Variable Mismatches
- YAML template variables must match `createStreamRequest()` data structure
- Oil enrichment has special handling for `suggested_oils` extraction

---

## Enhanced Testing Checklist

### Before Deployment: Comprehensive Validation

#### 1. TypeScript & Build Validation
- [ ] `npx tsc --noEmit` passes without errors
- [ ] All interfaces match YAML schemas exactly
- [ ] No legacy field names remain in codebase

#### 2. Response Parser Validation
- [ ] Add comprehensive logging to all response parsers
- [ ] Test each step and verify console logs show:
  - [ ] ✅ `[ResponseParser] Found [data_type]: X items`
  - [ ] ✅ `[ParallelEngine] Parsed result for ID: SUCCESS`
  - [ ] ❌ NO instances of `NO_RESULT` errors

#### 3. API Route Handler Validation
- [ ] Check `src/app/api/ai/streaming/route.ts` for legacy schema handling
- [ ] Verify no hardcoded old field names (e.g., `property_oil_suggestion`)
- [ ] Test API directly and verify response structure matches YAML

#### 4. End-to-End Data Flow Validation
- [ ] YAML Schema → AI Response structure matches
- [ ] API Route → Processes new schema correctly
- [ ] Streaming Service → Forwards structured data
- [ ] Response Parser → Extracts using correct paths
- [ ] Component → Displays data in UI
- [ ] State Management → Preserves all fields correctly

#### 5. Legacy Code Cleanup
- [ ] Search codebase for old field names: `grep -r "old_field_name" src/`
- [ ] Update or remove any legacy streaming hooks
- [ ] Update debug logging to use new schema field names
- [ ] Clean up any commented-out old code

### Per-Step Debugging Validation

#### Demographics Step
- [ ] Console shows: `[ResponseParser] Found potential_causes: X items`
- [ ] UI displays causes correctly
- [ ] No `extractFinalStreamingData` errors

#### Causes Step  
- [ ] Console shows: `[ResponseParser] Found potential_symptoms: X items`
- [ ] UI displays symptoms correctly
- [ ] Cause selection data flows to next step

#### Symptoms Step
- [ ] Console shows: `[ResponseParser] Found therapeutic_properties: X items`
- [ ] Properties display with correct field names
- [ ] Both symptoms and properties load if using dual hooks

#### Oil Suggestions Step ⚠️ **MOST COMMON FAILURE POINT**
- [ ] Console shows: `[ResponseParser] Found suggested_oils: X oils`
- [ ] Console shows: `[ResponseParser] Found therapeutic_property_context: true`
- [ ] Console shows: `[ParallelEngine] Parsed result for [ID]: SUCCESS`
- [ ] UI displays oils for each property
- [ ] NO `NO_RESULT` errors in console

#### Oil Enrichment Step
- [ ] Console shows: `[ResponseParser] Found enriched_oils: X oils`
- [ ] Safety information displays correctly
- [ ] Enrichment completes for all properties

### Error Scenario Testing
- [ ] Malformed AI responses don't crash the app
- [ ] Network errors are handled gracefully
- [ ] Missing fields don't cause runtime errors
- [ ] Loading states display correctly during streaming

---

## Emergency Rollback

If schema changes break the system:

1. **Immediate:** Revert the YAML schema file
2. **Quick:** Revert TypeScript type changes
3. **Thorough:** Revert all response parser changes
4. **Test:** Verify system works with original schema
5. **Debug:** Identify specific incompatibility before re-attempting

---

## Quick Reference: Schema Change Symptoms & Solutions

### 🚨 **"NO_RESULT" Errors**
```bash
# Symptom in console:
[ParallelEngine] Parsed result for ID: NO_RESULT

# Root Cause: Response parser path mismatch
# Solution: Add logging to response parser, check actual data structure
```

### 🚨 **Data Not Reaching Frontend**
```bash
# Symptom: Console shows successful API calls but no UI updates
# Root Cause: API route handler has legacy schema handling
# Solution: Check src/app/api/ai/streaming/route.ts for hardcoded field names
```

### 🚨 **TypeScript Compilation Errors**
```bash
# Symptom: Interface property errors
# Root Cause: TypeScript interfaces don't match YAML schema
# Solution: Update interfaces in recipe.types.ts to match YAML exactly
```

### 🚨 **Template Variable Errors**
```bash
# Symptom: AI receives wrong/missing data
# Root Cause: createStreamRequest() doesn't match YAML template variables
# Solution: Update api-data-transform.ts to provide correct data structure
```

## 🎯 CRITICAL FILES - PROVEN 3-STEP APPROACH

**Based on successful oil enrichment migration, these are the ONLY files you need to change 90% of the time:**

### 1. YAML Schema File ⚠️ **STEP 1**
```bash
src/features/create-recipe/prompts/[your-step].yaml
# Update the schema structure to match your desired AI output
```

### 2. TypeScript Types ⚠️ **STEP 2**
```bash
src/features/create-recipe/types/recipe.types.ts
# Add/update interfaces to match YAML schema exactly
```

### 3. Response Parser ⚠️ **STEP 3 - MOST CRITICAL**
```bash
src/features/create-recipe/hooks/use-create-recipe-streaming.ts
# Update responseParser paths to match new structure

# CURRENT WORKING PATHS:
# Oil Suggestions: updates.finalData?.data?.suggested_oils
# Oil Enrichment: updates.finalData?.data?.enriched_oils
```

### Optional Legacy Cleanup
```bash
# Only check if above 3 steps don't work:
src/app/api/ai/streaming/route.ts  # Remove old schema handling
```

**SEARCH FOR OLD REFERENCES:**
```bash
# Replace "old_field_name" with your actual old field name
grep -r "old_field_name" src/ --include="*.ts" --include="*.tsx"
```

## 🚨 INSTANT TROUBLESHOOTING

### Problem: "NO_RESULT" in Console
```bash
# Cause: Response parser path is wrong
# Solution: Add logging to see actual structure
responseParser: (updates) => {
  console.log('ACTUAL STRUCTURE:', JSON.stringify(updates.finalData, null, 2));
  // Then update your path based on what you see
}
```

### Problem: TypeScript Errors
```bash
# Cause: Interface doesn't match YAML schema
# Solution: Check interfaces in recipe.types.ts match YAML exactly
npx tsc --noEmit  # Shows exact error locations
```

### Problem: Data Not Reaching UI
```bash
# Cause: Response parser returning null/undefined
# Solution: Add success logging in responseParser
if (updates.finalData?.data?.your_field) {
  console.log('✅ SUCCESS: Found', updates.finalData.data.your_field.length, 'items');
  return updates.finalData.data.your_field;
}
```

**⚡ GOLDEN RULE**: If you see success logs in console but no UI update, the problem is NOT in the response parser!

---

## Contact

For questions about schema changes or streaming architecture, refer to:
- `src/lib/ai/hooks/` - Core streaming infrastructure
- `src/features/create-recipe/hooks/` - Feature-specific streaming logic
- This documentation for complete file dependency mapping 