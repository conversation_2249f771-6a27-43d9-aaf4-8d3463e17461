<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catálogo de Variações de UI/UX</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lora:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9;
            color: #334155;
        }
        .component-section {
            background-color: white;
            border-radius: 1.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.05), 0 4px 6px -4px rgb(0 0 0 / 0.05);
        }
        .section-title {
            font-family: 'Lora', serif;
            font-size: 2.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
            border-bottom: 2px solid #38b2ac;
            padding-bottom: 0.5rem;
        }
        .section-subtitle {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 2.5rem;
        }
        .variation-title {
            font-weight: 600;
            font-size: 1.25rem;
            color: #0d9488;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .font-lora {
            font-family: 'Lora', serif;
        }
        /* Estilos para componentes específicos */
        .modal {
            display: none;
        }
        .modal.is-open {
            display: flex;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 1.5rem;
            bottom: 1.5rem;
            width: 3px;
            background-color: #cbd5e1;
        }
        .timeline-item .dot {
            position: absolute;
            left: 1rem;
            top: 1.5rem;
            transform: translateX(-50%);
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 9999px;
            background-color: white;
            border: 3px solid #cbd5e1;
            transition: all 0.3s ease;
        }
        .timeline-item.active .dot {
            border-color: #38b2ac;
            background-color: #38b2ac;
        }
        /* Estilos para o carrossel */
        .carousel {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            gap: 1.5rem;
            padding-bottom: 1rem;
        }
        .carousel::-webkit-scrollbar {
            height: 8px;
        }
        .carousel::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 10px;
        }
        .carousel-item {
            flex: 0 0 90%;
            scroll-snap-align: center;
        }
        @media (min-width: 768px) {
            .carousel-item {
                flex: 0 0 45%;
            }
        }
        @media (min-width: 1024px) {
            .carousel-item {
                flex: 0 0 30%;
            }
        }
    </style>
</head>
<body class="p-4 md:p-8">

    <div class="max-w-7xl mx-auto">
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-slate-800 font-lora">Catálogo de Variações de UI/UX</h1>
            <p class="text-slate-500 mt-2 text-lg">Explorando novas abordagens para cada componente.</p>
        </header>

        <!-- =================================================================== -->
        <!-- Componente 1: Navegação por Abas                                    -->
        <!-- =================================================================== -->
        <section class="component-section">
            <h2 class="section-title">Componente 1: Navegação por Abas</h2>
            <p class="section-subtitle">Tornando a navegação mais moderna, suave e integrada ao tema de bem-estar.</p>

            <div class="space-y-12">
                <!-- Variação 1: Estilo "Pílula" -->
                <div>
                    <h3 class="variation-title">Variação 1: Estilo "Pílula" (Pill-shaped)</h3>
                    <nav class="relative bg-slate-100 p-2 rounded-full flex justify-center space-x-2">
                        <button class="px-6 py-2 rounded-full text-slate-500 font-semibold transition-colors">Visão Geral</button>
                        <button class="px-6 py-2 rounded-full text-white font-semibold bg-teal-500 shadow-md">Receitas</button>
                        <button class="px-6 py-2 rounded-full text-slate-500 font-semibold transition-colors">Estudos</button>
                        <button class="px-6 py-2 rounded-full text-slate-500 font-semibold transition-colors">Segurança</button>
                    </nav>
                </div>

                <!-- Variação 2: Abas Verticais -->
                <div>
                    <h3 class="variation-title">Variação 2: Abas Verticais</h3>
                    <div class="flex">
                        <nav class="flex flex-col space-y-2 border-r border-slate-200 pr-4">
                            <button class="flex items-center gap-3 text-left px-4 py-2 text-slate-500 hover:text-teal-600 transition-colors">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                <span>Visão Geral</span>
                            </button>
                            <button class="flex items-center gap-3 text-left px-4 py-2 text-teal-600 bg-teal-50 rounded-lg font-semibold">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" /></svg>
                                <span>Receitas</span>
                            </button>
                        </nav>
                        <div class="pl-6 text-slate-500">Conteúdo da aba apareceria aqui.</div>
                    </div>
                </div>
                
                <!-- Variação 3: Estilo "Segmented Control" -->
                <div>
                    <h3 class="variation-title">Variação 3: Estilo "Segmented Control"</h3>
                    <nav class="border border-slate-200 rounded-lg p-1 flex justify-between">
                        <button class="flex-1 text-center py-2 text-slate-500 font-medium rounded-md hover:bg-slate-100">Visão Geral</button>
                        <button class="flex-1 text-center py-2 text-teal-700 font-semibold bg-teal-100 rounded-md">Receitas</button>
                        <button class="flex-1 text-center py-2 text-slate-500 font-medium rounded-md hover:bg-slate-100">Estudos</button>
                    </nav>
                </div>

                <!-- Variação 4: Abas com Indicador de Progresso -->
                <div>
                    <h3 class="variation-title">Variação 4: Abas com Indicador de Progresso</h3>
                    <nav class="flex space-x-8 border-b border-slate-200">
                        <button class="flex items-center gap-2 py-3 border-b-2 border-teal-500 text-teal-600 font-semibold">
                            <span>Visão Geral</span>
                            <span class="h-2 w-2 bg-teal-500 rounded-full"></span>
                        </button>
                        <button class="flex items-center gap-2 py-3 border-b-2 border-transparent text-slate-500 hover:text-teal-600">
                            <span>Receitas</span>
                            <span class="h-2 w-2 bg-green-500 rounded-full" title="Completo"></span>
                        </button>
                        <button class="flex items-center gap-2 py-3 border-b-2 border-transparent text-slate-500 hover:text-teal-600">
                            <span>Segurança</span>
                            <span class="h-2 w-2 border-2 border-slate-400 rounded-full" title="Pendente"></span>
                        </button>
                    </nav>
                </div>

                <!-- Variação 5: Minimalista com Foco no Ícone -->
                <div>
                    <h3 class="variation-title">Variação 5: Minimalista com Foco no Ícone</h3>
                    <nav class="flex justify-center items-center gap-8 bg-white p-3 rounded-2xl shadow-inner">
                        <button class="p-3 rounded-full text-slate-400 hover:bg-slate-100 hover:text-teal-600">
                            <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        </button>
                        <button class="p-3 rounded-full text-white bg-teal-500 shadow-lg">
                            <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" /></svg>
                        </button>
                        <button class="p-3 rounded-full text-slate-400 hover:bg-slate-100 hover:text-teal-600">
                            <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
                        </button>
                    </nav>
                </div>
            </div>
        </section>

        <!-- =================================================================== -->
        <!-- Componente 3: Resumo dos Protocolos                                 -->
        <!-- =================================================================== -->
        <section class="component-section">
            <h2 class="section-title">Componente 3: Resumo dos Protocolos</h2>
            <p class="section-subtitle">Mantendo o conceito de "resumo vs. detalhe" de formas mais intuitivas e elegantes que o flip-card.</p>

            <!-- Variação 1: Card Expansível (Accordion) -->
            <div>
                <h3 class="variation-title">Variação 1: Card Expansível (Accordion)</h3>
                <div class="space-y-4 max-w-md mx-auto">
                    <details class="bg-slate-800 text-white rounded-2xl shadow-lg open:ring-4 open:ring-teal-300 transition">
                        <summary class="p-6 flex items-center gap-4 cursor-pointer">
                            <span class="text-3xl">🌅</span>
                            <div>
                                <h4 class="text-xl font-bold">Protocolo Matinal</h4>
                                <p class="text-sm text-slate-300">Sinergia para Foco & Calma</p>
                            </div>
                        </summary>
                        <div class="bg-white text-slate-700 p-6 rounded-b-2xl">
                            <p><strong>Objetivo:</strong> Gestão de estresse e prevenção.</p>
                            <ul class="mt-2 space-y-1 list-disc list-inside">
                                <li>Lavanda: 5 gotas</li>
                                <li>Olíbano: 3 gotas</li>
                                <li>Copaíba: 2 gotas</li>
                            </ul>
                        </div>
                    </details>
                </div>
            </div>

            <!-- Variação 2: Card com Modal -->
            <div>
                <h3 class="variation-title">Variação 2: Card com Modal</h3>
                <div class="max-w-sm mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 p-8 rounded-2xl text-white shadow-lg">
                    <span class="text-3xl">☀️</span>
                    <h4 class="text-2xl font-black mt-2">Protocolo Diurno</h4>
                    <p class="text-yellow-100">Alívio imediato da dor</p>
                    <button onclick="document.getElementById('modal-diurno').classList.add('is-open')" class="mt-6 w-full bg-white text-orange-600 font-semibold py-3 rounded-full shadow-md hover:bg-orange-100">Ver Detalhes</button>
                </div>
                <!-- Modal Structure -->
                <div id="modal-diurno" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center p-4 z-50">
                    <div class="bg-white rounded-2xl p-8 max-w-lg w-full relative">
                        <button onclick="document.getElementById('modal-diurno').classList.remove('is-open')" class="absolute top-4 right-4 text-slate-400 hover:text-slate-800">&times;</button>
                        <h4 class="text-2xl font-bold text-slate-800">Detalhes do Protocolo Diurno</h4>
                        <p class="mt-4"><strong>Objetivo:</strong> Alívio imediato da dor.</p>
                        <ul class="mt-2 space-y-1 list-disc list-inside">
                            <li>Lavanda: 4 gotas</li>
                            <li>Hortelã: 3 gotas</li>
                            <li>Copaíba: 3 gotas</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Variação 3: Carrossel Horizontal -->
            <div>
                <h3 class="variation-title">Variação 3: Carrossel Horizontal</h3>
                <div class="carousel">
                    <!-- Card 1 -->
                    <div class="carousel-item bg-slate-800 text-white p-6 rounded-2xl flex flex-col">
                        <h4 class="text-xl font-bold">🌅 Matinal</h4>
                        <p class="flex-1 mt-2 text-slate-300">Sinergia para Foco & Calma.</p>
                        <button class="mt-4 text-left font-semibold text-teal-300">Ver detalhes &rarr;</button>
                    </div>
                    <!-- Card 2 -->
                    <div class="carousel-item bg-orange-500 text-white p-6 rounded-2xl flex flex-col">
                        <h4 class="text-xl font-bold">☀️ Diurno</h4>
                        <p class="flex-1 mt-2 text-orange-100">Alívio imediato da dor.</p>
                        <button class="mt-4 text-left font-semibold text-white">Ver detalhes &rarr;</button>
                    </div>
                    <!-- Card 3 -->
                    <div class="carousel-item bg-indigo-800 text-white p-6 rounded-2xl flex flex-col">
                        <h4 class="text-xl font-bold">🌙 Noturno</h4>
                        <p class="flex-1 mt-2 text-indigo-200">Sono reparador e relaxamento.</p>
                        <button class="mt-4 text-left font-semibold text-indigo-300">Ver detalhes &rarr;</button>
                    </div>
                </div>
            </div>

            <!-- Variação 4: Visualização em Timeline -->
            <div>
                <h3 class="variation-title">Variação 4: Visualização em Timeline</h3>
                <div class="relative timeline max-w-md mx-auto">
                    <!-- Item 1 -->
                    <div class="timeline-item relative pl-10 pb-8 active">
                        <div class="dot"></div>
                        <div class="bg-slate-100 p-4 rounded-lg">
                            <p class="text-sm text-slate-500">07:00 - 09:00</p>
                            <h4 class="font-bold text-slate-800">Protocolo Matinal</h4>
                            <p class="text-sm">Detalhes sobre foco e calma...</p>
                        </div>
                    </div>
                    <!-- Item 2 -->
                    <div class="timeline-item relative pl-10 pb-8">
                        <div class="dot"></div>
                        <p class="text-sm text-slate-500">Durante o dia</p>
                        <h4 class="font-bold text-slate-800">Protocolo Diurno</h4>
                    </div>
                    <!-- Item 3 -->
                    <div class="timeline-item relative pl-10">
                        <div class="dot"></div>
                        <p class="text-sm text-slate-500">22:00 - 23:00</p>
                        <h4 class="font-bold text-slate-800">Protocolo Noturno</h4>
                    </div>
                </div>
            </div>

            <!-- Variação 5: Hover com Overlay Translúcido -->
            <div>
                <h3 class="variation-title">Variação 5: Hover com Overlay Translúcido</h3>
                <div class="relative max-w-sm mx-auto rounded-2xl overflow-hidden shadow-lg group">
                    <img src="https://placehold.co/600x400/31087B/FFFFFF?text=Relaxamento" alt="Imagem de fundo para o protocolo noturno" class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-indigo-900 bg-opacity-70 flex flex-col p-8 text-white">
                        <span class="text-3xl">🌙</span>
                        <h4 class="text-2xl font-black mt-2">Protocolo Noturno</h4>
                        <p class="text-indigo-200">Sono reparador e relaxamento.</p>
                    </div>
                    <div class="absolute inset-0 bg-indigo-900 bg-opacity-95 p-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center">
                        <h5 class="font-bold">Detalhes</h5>
                        <ul class="mt-2 space-y-1 list-disc list-inside text-sm">
                            <li>Lavanda: 6 gotas</li>
                            <li>Camomila Romana: 3 gotas</li>
                            <li>Erva-cidreira: 1 gota</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- =================================================================== -->
        <!-- Componente 5: Protocolo de Segurança                                -->
        <!-- =================================================================== -->
        <section class="component-section">
            <h2 class="section-title">Componente 5: Protocolo de Segurança</h2>
            <p class="section-subtitle">Refinando a apresentação visual para ser ainda mais clara, sem perder a seriedade.</p>

            <!-- Variação 1: Design com Ícones Grandes -->
            <div>
                <h3 class="variation-title">Variação 1: Design com Ícones Grandes</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="flex items-start gap-4 p-4 bg-yellow-50 rounded-lg">
                        <div class="text-yellow-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 102 0V6zM10 14a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" /></svg></div>
                        <div>
                            <h4 class="font-bold text-yellow-900">Teste de Sensibilidade</h4>
                            <p class="text-sm text-yellow-800">Aplique uma gota no antebraço e aguarde 24h.</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 p-4 bg-blue-50 rounded-lg">
                        <div class="text-blue-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" viewBox="0 0 20 20" fill="currentColor"><path d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" /></svg></div>
                        <div>
                            <h4 class="font-bold text-blue-900">Uso Seguro</h4>
                            <p class="text-sm text-blue-800">Sempre dilua e mantenha longe dos olhos.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Variação 2: Layout de "Dos and Don'ts" -->
            <div>
                <h3 class="variation-title">Variação 2: Layout de "Dos and Don'ts"</h3>
                <div class="bg-slate-50 p-6 rounded-lg">
                    <h4 class="font-bold text-slate-800 text-lg mb-4">Armazenamento Correto</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-3">
                            <div class="flex items-start gap-3">
                                <span class="text-green-500 mt-1">✅</span>
                                <div><strong class="text-slate-700">Fazer:</strong> Guardar em frascos de vidro escuro, em local fresco e ao abrigo da luz.</div>
                            </div>
                            <div class="flex items-start gap-3">
                                <span class="text-green-500 mt-1">✅</span>
                                <div><strong class="text-slate-700">Fazer:</strong> Manter as tampas bem fechadas.</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-start gap-3">
                                <span class="text-red-500 mt-1">❌</span>
                                <div><strong class="text-slate-700">Não Fazer:</strong> Deixar ao alcance de crianças e animais.</div>
                            </div>
                             <div class="flex items-start gap-3">
                                <span class="text-red-500 mt-1">❌</span>
                                <div><strong class="text-slate-700">Não Fazer:</strong> Expor ao calor ou luz solar direta.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Variação 3: Accordion de Segurança -->
            <div>
                <h3 class="variation-title">Variação 3: Accordion de Segurança</h3>
                <div class="space-y-2">
                    <details class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                        <summary class="font-bold text-yellow-900 cursor-pointer">1. Teste de Sensibilidade (Obrigatório)</summary>
                        <p class="mt-2 text-sm text-yellow-800">Antes do primeiro uso, aplique uma gota na parte interna do antebraço...</p>
                    </details>
                    <details class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                        <summary class="font-bold text-blue-900 cursor-pointer">2. Uso Seguro e Armazenamento</summary>
                        <p class="mt-2 text-sm text-blue-800">Sempre dilua os óleos essenciais em um óleo carreador...</p>
                    </details>
                    <details class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                        <summary class="font-bold text-red-900 cursor-pointer">3. Sinais de Alerta e Ação Necessária</summary>
                        <p class="mt-2 text-sm text-red-800">Procure ajuda médica imediata se ocorrer uma reação alérgica grave...</p>
                    </details>
                </div>
            </div>

            <!-- Variação 4: Checklist de Confirmação -->
            <div>
                <h3 class="variation-title">Variação 4: Checklist de Confirmação</h3>
                <div class="bg-slate-50 p-6 rounded-lg space-y-4">
                    <h4 class="font-bold text-slate-800 text-lg">Confirmação de Leitura</h4>
                    <label class="flex items-start gap-3 cursor-pointer">
                        <input type="checkbox" class="h-5 w-5 rounded border-slate-300 text-teal-500 focus:ring-teal-500 mt-1">
                        <span class="text-slate-700">Li e compreendi a necessidade do <strong>Teste de Sensibilidade</strong> antes de usar qualquer mistura.</span>
                    </label>
                    <label class="flex items-start gap-3 cursor-pointer">
                        <input type="checkbox" class="h-5 w-5 rounded border-slate-300 text-teal-500 focus:ring-teal-500 mt-1">
                        <span class="text-slate-700">Estou ciente de que devo manter os óleos <strong>fora do alcance de crianças</strong> e animais.</span>
                    </label>
                    <button class="bg-teal-500 text-white font-bold py-2 px-6 rounded-full hover:bg-teal-600 disabled:bg-slate-300" disabled>Confirmar e Prosseguir</button>
                </div>
            </div>

            <!-- Variação 5: Estilo "Cartão de Alerta" -->
            <div>
                <h3 class="variation-title">Variação 5: Estilo "Cartão de Alerta"</h3>
                <div class="border-4 border-red-500 rounded-lg p-6 relative bg-red-50">
                    <div class="absolute -top-4 left-1/2 -translate-x-1/2 bg-red-500 text-white px-4 py-1 rounded-full text-sm font-bold tracking-wider">ALERTA MÉDICO</div>
                    <h4 class="text-xl font-bold text-red-900 text-center mt-4">Procure Ajuda Médica Imediata se:</h4>
                    <ul class="mt-4 list-disc list-inside space-y-2 text-red-800">
                        <li>Ocorrer uma <strong>reação alérgica grave</strong> (inchaço no rosto ou garganta).</li>
                        <li>Surgirem <strong>dificuldades respiratórias</strong> após o uso.</li>
                        <li>Os sintomas que você está tratando piorarem drasticamente.</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Script para o checklist de confirmação
        const checklistSection = document.querySelector('h3.variation-title + div:has(input[type="checkbox"])');
        if (checklistSection) {
            const checkboxes = checklistSection.querySelectorAll('input[type="checkbox"]');
            const confirmButton = checklistSection.querySelector('button');
            
            const checkCompletion = () => {
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                confirmButton.disabled = !allChecked;
            };

            checkboxes.forEach(cb => cb.addEventListener('change', checkCompletion));
        }
    </script>
</body>
</html>
