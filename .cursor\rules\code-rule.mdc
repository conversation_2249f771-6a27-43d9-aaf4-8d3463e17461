---
alwaysApply: true
---
# Cursor Rule: Clean Code Principles

## Core Principles

### KISS (Keep It Simple, Stupid)
- Prefer simple, clear solutions over complex ones.
- Simplify when possible without sacrificing clarity or correctness.

### DRY (Don’t Repeat Yourself)
- Centralize repeated logic and functionality.
- Example: Use one tax calculation function, not multiple.

### YAGNI (You Aren’t Gonna Need It)
- Only implement what's needed now.
- Avoid speculative features that may never be used.

### SOLID
- **S – Single Responsibility:** Each unit does one thing.
- **O – Open/Closed:** Extend without modifying existing code.
- **L – Liskov Substitution:** Subtypes must not break base-type behavior.
- **I – Interface Segregation:** Favor small, focused interfaces.
- **D – Dependency Inversion:** Depend on abstractions, not concretes.

## Clean Code Guidelines

### Constants Over Magic Numbers
- Replace hard-coded values with named constants.
- Use descriptive names and store them in a logical location.

### Meaningful Names
- Names should convey purpose and usage.
- Avoid non-standard abbreviations.

### Smart Comments
- Explain *why*, not *what*.
- Use for APIs, complex logic, or non-obvious behavior.

### Structure
- Group related code.
- Follow logical, consistent naming and folder conventions.

## Editing Guidelines

### Edit Behavior
1. One logical change per edit.
2. Each step must be independently testable.
3. Specify order when changes are dependent (e.g., "Do X, then Y").

### Format
- Deliver edits in a **smaller chunks** per file.
- Do **not** include explanations, summaries, or steps.

## Do Not:
- Apologize
- Offer understanding feedback
- Suggest whitespace-only changes
- Summarize changes
- Invent changes not requested
- Ask for confirmation of known info
- Remove unrelated code
- Discuss current implementation (unless asked)
- Recommend unnecessary updates
- Ask for implementation checks

## Additional Rules
- Always verify information; avoid speculation.
- Provide real file links (not x.md).
- Optimize performance: minimize `use client`, `useEffect`, and `setState`; prefer React Server Components (RSC).
- Enforce bracket notation for index signature types, so it must be accessed with ['X'].
