/**
 * @fileoverview API helpers for AI routes
 * Common utilities for API key validation, request parsing, and error handling
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * Validates that OpenAI API key is configured
 * @throws Error if API key is not configured
 */
export function validateOpenAIKey(): void {
  if (!process.env['OPENAI_API_KEY']) {
    throw new Error('OpenAI API key not configured');
  }
}

/**
 * Parses JSON body from NextRequest with error handling
 * @param request - The NextRequest object
 * @returns Parsed JSON body
 * @throws Error if JSON parsing fails
 */
export async function parseJsonBody(request: NextRequest): Promise<any> {
  try {
    return await request.json();
  } catch (error) {
    throw new Error('Invalid JSON in request body');
  }
}

/**
 * Creates a standardized error response
 * @param message - Error message
 * @param status - HTTP status code (default: 500)
 * @param statusText - HTTP status text (optional)
 * @returns NextResponse with error
 */
export function createErrorResponse(
  message: string, 
  status: number = 500, 
  statusText?: string
): NextResponse {
  return NextResponse.json(
    { 
      error: message,
      timestamp: new Date().toISOString()
    }, 
    { 
      status,
      statusText,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    }
  );
}

/**
 * Creates a standardized success response
 * @param data - Response data
 * @param status - HTTP status code (default: 200)
 * @returns NextResponse with success data
 */
export function createSuccessResponse(
  data: any, 
  status: number = 200
): NextResponse {
  return NextResponse.json(data, {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}

/**
 * Wraps an API handler with common error handling
 * @param handler - The API handler function
 * @returns Wrapped handler with error handling
 */
export function withErrorHandling(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      return await handler(request);
    } catch (error) {
      console.error('API Error:', error);
      
      if (error instanceof Error) {
        // Handle specific known errors
        if (error.message.includes('OpenAI API key not configured')) {
          return createErrorResponse(
            'AI service is not properly configured. Please contact support.',
            503,
            'Service Unavailable'
          );
        }
        
        if (error.message.includes('Invalid JSON')) {
          return createErrorResponse(
            'Invalid request format. Please check your request body.',
            400,
            'Bad Request'
          );
        }
        
        // Generic error with message
        return createErrorResponse(error.message, 500);
      }
      
      // Unknown error
      return createErrorResponse(
        'An unexpected error occurred. Please try again later.',
        500
      );
    }
  };
} 