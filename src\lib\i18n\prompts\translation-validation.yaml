# Translation Validation Agent Configuration
# This YAML file contains the complete configuration for the AI agent that validates
# and quality-checks translated internationalization files.

version: "1.0.0"
description: "Quality assurance agent for validating i18n translations with medical and wellness terminology expertise"

# Agent Configuration
config:
  model: "gpt-4.1-nano"
  temperature: 0.1  # Very conservative for validation
  max_tokens: 6000
  top_p: 0.9
  frequency_penalty: 0.0
  presence_penalty: 0.0
  timeout_seconds: 90

# System Prompt Template
template: |
  **Persona:** Act as a senior translation quality assurance specialist with expertise in medical/wellness terminology, web application localization, and essential oils/aromatherapy domain knowledge. You have experience validating translations for health-focused applications.

  **Objective:** Thoroughly validate the provided translated i18n file against the original English version, checking for accuracy, consistency, cultural appropriateness, and technical correctness.

  **Application Context:**
  - **Domain:** Essential oils and aromatherapy web application
  - **Critical Areas:** Safety information, medical terminology, user interface elements
  - **Quality Standards:** Professional medical-grade accuracy with user-friendly accessibility

  **Input Data:**

  1. `original_file`: The original English i18n JSON file
     * Value: `{{original_file}}`

  2. `translated_file`: The translated i18n JSON file to validate
     * Value: `{{translated_file}}`

  3. `target_language`: The target language of the translation
     * Value: `{{target_language}}`

  4. `validation_focus`: Specific areas to focus validation on
     * Value: `{{validation_focus}}`

  **Validation Criteria:**

  1. **Technical Validation:**
     - JSON structure integrity (keys, nesting, syntax)
     - Variable placeholder preservation (`{current}`, `{total}`, etc.)
     - Context field preservation (`_context` fields remain in English)
     - No missing or extra translation keys

  2. **Content Accuracy:**
     - Meaning preservation from original to translation
     - Medical/wellness terminology accuracy
     - Safety information correctness (critical for user safety)
     - Essential oil name handling (botanical vs. common names)

  3. **Language Quality:**
     - Natural, fluent language (not machine-translated sounding)
     - Grammatical correctness and proper punctuation
     - Consistent terminology throughout the file
     - Appropriate formality level for health applications

  4. **Cultural Appropriateness:**
     - Culturally sensitive health terminology
     - Appropriate address forms (formal/informal)
     - Regional variant correctness (Brazilian Portuguese vs. European, Latin American Spanish vs. European)
     - Cultural context for wellness practices

  5. **User Experience:**
     - UI element translations follow target language conventions
     - Form field labels are clear and standard
     - Error messages are helpful and clear
     - Navigation terms are intuitive

  6. **Domain-Specific Validation:**
     - Essential oil safety warnings are accurately translated
     - Therapeutic property descriptions maintain scientific accuracy
     - Age category labels are culturally appropriate
     - Health concern terminology is medically sound

  **Critical Safety Areas (Zero Tolerance for Errors):**
  - Pregnancy and nursing safety warnings
  - Child safety information
  - Dilution requirements and ratios
  - Phototoxicity warnings
  - Internal use contraindications

  **Validation Process:**
  1. Compare structure and completeness
  2. Validate critical safety information
  3. Check medical/wellness terminology accuracy
  4. Assess language quality and cultural appropriateness
  5. Verify technical elements (variables, JSON structure)
  6. Provide improvement recommendations

  **Output Requirements:**
  - Comprehensive validation report with specific issues identified
  - Severity classification for each issue (Critical, High, Medium, Low)
  - Specific recommendations for improvements
  - Overall quality assessment and approval status

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "validation_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          validation_id:
            type: "string"
            description: "Unique identifier for this validation"
          target_language:
            type: "string"
            description: "Language that was validated"
          validator_version:
            type: "string"
            description: "Version of the validation agent"
          timestamp_utc:
            type: "string"
            description: "Timestamp of validation completion"
          validation_scope:
            type: "string"
            description: "Scope of validation performed"
        required: ["validation_id", "target_language", "validator_version", "timestamp_utc", "validation_scope"]
        additionalProperties: false
      overall_assessment:
        type: "object"
        properties:
          quality_score:
            type: "number"
            minimum: 0
            maximum: 100
            description: "Overall quality score (0-100)"
          approval_status:
            type: "string"
            enum: ["approved", "approved_with_minor_fixes", "needs_revision", "rejected"]
            description: "Overall approval status"
          critical_issues_count:
            type: "integer"
            description: "Number of critical issues found"
          high_issues_count:
            type: "integer"
            description: "Number of high priority issues found"
          medium_issues_count:
            type: "integer"
            description: "Number of medium priority issues found"
          low_issues_count:
            type: "integer"
            description: "Number of low priority issues found"
          summary:
            type: "string"
            description: "Executive summary of validation results"
        required: ["quality_score", "approval_status", "critical_issues_count", "high_issues_count", "medium_issues_count", "low_issues_count", "summary"]
        additionalProperties: false
      technical_validation:
        type: "object"
        properties:
          json_structure_valid:
            type: "boolean"
            description: "Whether JSON structure is valid and complete"
          variables_preserved:
            type: "boolean"
            description: "Whether all variable placeholders are preserved"
          context_fields_preserved:
            type: "boolean"
            description: "Whether _context fields remain in English"
          key_completeness:
            type: "boolean"
            description: "Whether all translation keys are present"
          issues:
            type: "array"
            items:
              type: "object"
              properties:
                key_path:
                  type: "string"
                  description: "JSON path to the issue"
                issue_type:
                  type: "string"
                  description: "Type of technical issue"
                description:
                  type: "string"
                  description: "Description of the issue"
                severity:
                  type: "string"
                  enum: ["critical", "high", "medium", "low"]
                  description: "Severity level"
              required: ["key_path", "issue_type", "description", "severity"]
              additionalProperties: false
        required: ["json_structure_valid", "variables_preserved", "context_fields_preserved", "key_completeness", "issues"]
        additionalProperties: false
      content_validation:
        type: "object"
        properties:
          safety_information_accurate:
            type: "boolean"
            description: "Whether safety information is accurately translated"
          medical_terminology_appropriate:
            type: "boolean"
            description: "Whether medical terms are appropriate for target language"
          meaning_preserved:
            type: "boolean"
            description: "Whether original meaning is preserved"
          issues:
            type: "array"
            items:
              type: "object"
              properties:
                key_path:
                  type: "string"
                  description: "JSON path to the content issue"
                original_text:
                  type: "string"
                  description: "Original English text"
                translated_text:
                  type: "string"
                  description: "Translated text with issue"
                issue_description:
                  type: "string"
                  description: "Description of the content issue"
                suggested_fix:
                  type: "string"
                  description: "Suggested correction"
                severity:
                  type: "string"
                  enum: ["critical", "high", "medium", "low"]
                  description: "Severity level"
              required: ["key_path", "original_text", "translated_text", "issue_description", "suggested_fix", "severity"]
              additionalProperties: false
        required: ["safety_information_accurate", "medical_terminology_appropriate", "meaning_preserved", "issues"]
        additionalProperties: false
      language_quality:
        type: "object"
        properties:
          fluency_score:
            type: "number"
            minimum: 0
            maximum: 10
            description: "Fluency score (0-10)"
          consistency_score:
            type: "number"
            minimum: 0
            maximum: 10
            description: "Terminology consistency score (0-10)"
          cultural_appropriateness_score:
            type: "number"
            minimum: 0
            maximum: 10
            description: "Cultural appropriateness score (0-10)"
          issues:
            type: "array"
            items:
              type: "object"
              properties:
                key_path:
                  type: "string"
                  description: "JSON path to the language issue"
                issue_type:
                  type: "string"
                  enum: ["grammar", "fluency", "consistency", "cultural", "formality"]
                  description: "Type of language issue"
                description:
                  type: "string"
                  description: "Description of the language issue"
                suggested_improvement:
                  type: "string"
                  description: "Suggested improvement"
                severity:
                  type: "string"
                  enum: ["critical", "high", "medium", "low"]
                  description: "Severity level"
              required: ["key_path", "issue_type", "description", "suggested_improvement", "severity"]
              additionalProperties: false
        required: ["fluency_score", "consistency_score", "cultural_appropriateness_score", "issues"]
        additionalProperties: false
      recommendations:
        type: "object"
        properties:
          immediate_fixes:
            type: "array"
            items:
              type: "string"
            description: "Issues that must be fixed before approval"
          suggested_improvements:
            type: "array"
            items:
              type: "string"
            description: "Recommended improvements for better quality"
          terminology_glossary:
            type: "array"
            items:
              type: "object"
              properties:
                english_term:
                  type: "string"
                  description: "English term"
                recommended_translation:
                  type: "string"
                  description: "Recommended translation"
                context:
                  type: "string"
                  description: "Context for usage"
              required: ["english_term", "recommended_translation", "context"]
              additionalProperties: false
            description: "Terminology recommendations for consistency"
          next_steps:
            type: "array"
            items:
              type: "string"
            description: "Recommended next steps"
        required: ["immediate_fixes", "suggested_improvements", "terminology_glossary", "next_steps"]
        additionalProperties: false
    required: ["meta", "overall_assessment", "technical_validation", "content_validation", "language_quality", "recommendations"]
    additionalProperties: false

# Validation Severity Definitions
severity_definitions:
  critical:
    description: "Issues that could cause user harm or complete application failure"
    examples:
      - "Incorrect safety warnings"
      - "Missing essential safety information"
      - "JSON syntax errors"
      - "Critical medical terminology errors"
    
  high:
    description: "Issues that significantly impact user experience or accuracy"
    examples:
      - "Incorrect medical terminology"
      - "Missing variable placeholders"
      - "Inconsistent key terminology"
      - "Cultural inappropriateness"
    
  medium:
    description: "Issues that affect quality but don't prevent functionality"
    examples:
      - "Minor grammatical errors"
      - "Suboptimal word choices"
      - "Minor consistency issues"
      - "Formality level mismatches"
    
  low:
    description: "Minor improvements that enhance overall quality"
    examples:
      - "Style preferences"
      - "Minor punctuation issues"
      - "Optional cultural adaptations"
      - "Enhanced clarity suggestions"

# Quality Benchmarks by Language
quality_benchmarks:
  portuguese_brazil:
    minimum_quality_score: 85
    critical_safety_terms:
      - "gravidez" # pregnancy
      - "amamentação" # nursing
      - "crianças" # children
      - "diluição" # dilution
      - "fotossensível" # photosensitive
    
  spanish_latin_america:
    minimum_quality_score: 85
    critical_safety_terms:
      - "embarazo" # pregnancy
      - "lactancia" # nursing
      - "niños" # children
      - "dilución" # dilution
      - "fotosensible" # photosensitive 