'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, ChevronDown, ChevronUp, Sparkles, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { EssentialOil } from '@/features/create-recipe/types/recipe.types';

interface InlineOilSuggestionsProps {
  oils: EssentialOil[];
  isLoading: boolean;
  error: string | null;
  onAnalyze: () => void;
  className?: string;
  compact?: boolean;
}

export function InlineOilSuggestions({
  oils = [],
  isLoading,
  error,
  onAnalyze,
  className,
  compact = false,
}: InlineOilSuggestionsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // In compact mode, we don't show the expandable details
  const showExpandable = !compact;

  // Loading state
  if (isLoading) {
    return compact ? (
      <div className="flex items-center space-x-2">
        <Loader2 className="h-3.5 w-3.5 animate-spin text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Analyzing...</span>
      </div>
    ) : (
      <div className={cn("p-3 rounded-lg bg-muted/20", className)}>
        <div className="flex items-center text-muted-foreground text-sm">
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          <span>Analyzing essential oils...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("p-3 rounded-lg bg-destructive/10 border border-destructive/20", className)}>
        <div className="flex items-center text-destructive text-sm">
          <AlertCircle className="h-4 w-4 mr-2" />
          <span>Failed to load suggestions</span>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto h-6 text-xs"
            onClick={onAnalyze}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }



  // Empty state
  if (oils.length === 0) {
    return compact ? (
      <Button
        variant="ghost"
        size="sm"
        className={cn("h-7 text-xs", className)}
        onClick={onAnalyze}
      >
        <Sparkles className="h-3 w-3 mr-1.5" />
        <span>Get Oils</span>
      </Button>
    ) : (
      <Button
        variant="outline"
        size="sm"
        className={cn("h-8", className)}
        onClick={onAnalyze}
      >
        <Sparkles className="h-3.5 w-3.5 mr-2" />
        Analyze Oils
      </Button>
    );
  }

  const visibleOils = isExpanded ? oils : oils.slice(0, 3);
  const hasMore = oils.length > 3;

  // In compact mode, just show the first 3 oils with no expand/collapse
  if (compact) {
    return (
      <div className="flex flex-wrap items-center gap-1.5">
        {oils.slice(0, 3).map((oil) => (
          <Badge
            key={oil.oil_id}
            variant="outline"
            className="text-xs py-0.5 px-1.5 rounded-md border-muted-foreground/30 hover:bg-muted/50 transition-colors h-5"
          >
            {oil.name_localized || oil.name_english}
            {oil.relevancy_to_property_score && (
              <span className="ml-1 text-muted-foreground/80 text-[0.7rem]">
                {oil.relevancy_to_property_score.toFixed(1)}
              </span>
            )}
          </Badge>
        ))}
        {oils.length > 3 && (
          <span className="text-xs text-muted-foreground">+{oils.length - 3} more</span>
        )}
      </div>
    );
  }

  // Full expanded view
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap items-center gap-1.5">
        {visibleOils.map((oil) => (
          <Badge
            key={oil.oil_id}
            variant="outline"
            className="text-xs py-1 px-2 rounded-md border-muted-foreground/30 hover:bg-muted/50 transition-colors"
          >
            {oil.name_localized || oil.name_english}
            {oil.relevancy_to_property_score && (
              <span className="ml-1.5 text-muted-foreground/80">
                {oil.relevancy_to_property_score.toFixed(1)}
              </span>
            )}
          </Badge>
        ))}
        {hasMore && showExpandable && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs text-muted-foreground"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronUp className="h-3.5 w-3.5 mr-1" />
            ) : (
              <ChevronDown className="h-3.5 w-3.5 mr-1" />
            )}
            {isExpanded ? 'Show less' : `+${oils.length - 3} more`}
          </Button>
        )}
      </div>

      {showExpandable && (
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="mt-2 space-y-2">
                {oils.map((oil) => (
                  <div
                    key={oil.oil_id}
                    className="p-3 text-sm rounded-lg bg-muted/10 border border-muted/30"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">
                          {oil.name_localized || oil.name_english}
                          {oil.name_botanical && (
                            <span className="text-xs text-muted-foreground ml-2">
                              ({oil.name_botanical})
                            </span>
                          )}
                        </h4>
                        {oil.match_rationale_localized && (
                          <p className="mt-1 text-muted-foreground">
                            {oil.match_rationale_localized}
                          </p>
                        )}
                      </div>
                      {oil.relevancy_to_property_score !== undefined && (
                        <Badge variant="outline" className="ml-2">
                          {oil.relevancy_to_property_score.toFixed(1)}/5
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
}
