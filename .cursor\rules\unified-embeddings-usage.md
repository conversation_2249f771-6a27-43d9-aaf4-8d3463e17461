---
description: Rules for using the UnifiedEmbeddingsService across the application. This service handles both single and batch embedding operations with standardized error handling.
globs: 
  - src/lib/ai/services/**/*.ts
  - src/features/**/services/**/*.ts
alwaysApply: false
---

# Unified Embeddings Service Rules

## Required Import
```typescript
import { getBatchEmbeddingsService } from '../lib/ai/services/unified-embeddings.service';
```

## Rules

1. Service Initialization
- Store service instance as a class property
- Initialize in constructor using factory method
- Use proper type: `ReturnType<typeof getBatchEmbeddingsService>`

2. Method Usage
- Single: `createSingleEmbedding({ text })`
- Batch: `createBatchEmbeddings({ texts: string[] })`
- Always await responses
- Check response length matches input

3. Response Handling
- Access embeddings via `response.embedding` or `responses[i].embedding`
- Each response includes `model` and `usage` data
- Handle empty/invalid responses

4. Error Handling
- Wrap calls in try/catch
- Log errors with service prefix
- Include error context in thrown errors 