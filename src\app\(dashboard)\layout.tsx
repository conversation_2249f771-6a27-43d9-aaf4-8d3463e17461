import { HydrationBoundary } from '@tanstack/react-query';
import { getServerAuthWithProfilePrefetch } from '@/lib/auth/server-auth.utils';
import { getServerLogger } from '@/lib/logger';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { DashboardAppSidebar } from '@/features/dashboard/components/dashboard-app-sidebar';
import { DashboardHeader } from '@/features/dashboard/components';
import { LoadingProvider as DashboardLoadingProvider } from "@/features/ui/providers/loading-provider";
import { headers } from 'next/headers';

const logger = getServerLogger('DashboardLayout');
const shouldDebugServer = process.env.NODE_ENV === 'development' && 
                          process.env['NEXT_PUBLIC_DEBUG_SERVER'] === 'true';

/**
 * Shared layout for all routes within the (dashboard) group.
 * This layout is responsible for:
 * 1. Applying the main visual shell (DashboardLayoutComponent).
 * 2. Prefetching common data, like the user profile, for all dashboard pages
 *    if a user session exists. This data is then passed to the client via HydrationBoundary.
 * 
 * Optimized to prevent redundant server-side operations during streaming:
 * - Uses centralized server auth utility (DRY principle)
 * - All auth and profile logic is cached and centralized
 * - Reduces console noise during development
 */
export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get request headers to create a unique request identifier for debugging
  const headersList = await headers();
  const requestId = headersList.get('x-request-id') || 
                   headersList.get('x-trace-id') || 
                   `req-${Date.now()}`;
  
  // Reduce console noise - only log if explicitly enabled
  if (shouldDebugServer) {
    console.log(`[${new Date().toISOString()}] DashboardLayout (Server): Start (${requestId})`);
  }
  
  // Use centralized server auth utility (follows DRY principle)
  const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
    prefetchProfile: true,
    profileTimeout: 500,
    profileStaleTime: 10 * 1000,
    requestId,
    debugMode: shouldDebugServer
  });

  if (error) {
    logger.error('DashboardLayout (Server): Auth error from centralized utility.', { 
      error: error.message,
      requestId 
    });
    // Continue without user context if auth fails
  }
  
  // Only log final state if debugging is explicitly enabled
  if (shouldDebugServer) {
    console.log(`[${new Date().toISOString()}] DashboardLayout (Server): Auth completed - User: ${user ? 'authenticated' : 'none'} (${requestId})`);
  }

  return (
    <DashboardLoadingProvider>
      <SidebarProvider initialVariant="inset" initialCollapsible="offcanvas">
        <DashboardAppSidebar />
        <SidebarInset className="bg-background">
          <DashboardHeader />
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  <HydrationBoundary state={dehydratedState}>
                    {children}
                  </HydrationBoundary>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </DashboardLoadingProvider>
  );
}
