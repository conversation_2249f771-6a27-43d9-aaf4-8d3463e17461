# Step Naming Improvements: Future Implementation Plan

## Overview

This document outlines a plan to improve the naming conventions for the Create Recipe feature's step names, prompt files, and hook methods. The goal is to make the codebase more intuitive and maintainable without changing the underlying schemas or data structures.

**Note**: This document focuses only on **naming improvements**. For schema structure changes, refer to `schema-change-guide.md`.

## Current Context (As of Implementation Date)

### Current Step Flow
The Create Recipe feature follows this 5-step process:

1. **Demographics** → Analyzes health concern to find root causes
2. **Causes** → Discovers related symptoms based on selected causes  
3. **Symptoms** → Identifies therapeutic properties to address symptoms
4. **Properties** → Recommends essential oils for each property (parallel)
5. **Enrichment** → Adds safety information to recommended oils (parallel)

### Current Naming Convention

#### YAML Prompt Files
```
src/features/create-recipe/prompts/
├── potential-causes.yaml
├── potential-symptoms.yaml  
├── therapeutic-properties.yaml
├── suggested-oils.yaml
└── oil-enrichment.yaml
```

#### API Step Names
```typescript
// Used in createStreamRequest() calls
'potential-causes'
'potential-symptoms'  
'therapeutic-properties'
'suggested-oils'
'oil-enrichment'
```

#### Hook Names
```typescript
// Core infrastructure
useAIStreaming()
useParallelStreamingEngine()

// Feature-specific
useCreateRecipeStreaming()
  - startOilSuggestionStreaming()
  - startOilEnrichmentStreaming()
```

### Current Streaming Architecture
- **Single-Stream Steps**: Demographics, Causes, Symptoms use `useAIStreaming`
- **Parallel-Stream Steps**: Properties, Enrichment use `useCreateRecipeStreaming` → `useParallelStreamingEngine`

---

## Proposed Naming Improvements

### 1. YAML Prompt Files

#### Current → Improved
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `potential-causes.yaml` | `health-concern-analysis.yaml` | More descriptive - analyzes health concerns to find root causes |
| `potential-symptoms.yaml` | `symptom-discovery.yaml` | Clearer purpose - discovers related symptoms |
| `therapeutic-properties.yaml` | `healing-properties-analysis.yaml` | More human-readable and specific |
| `suggested-oils.yaml` | `oil-recommendations.yaml` | "Recommendations" is clearer than "suggestions" |
| `oil-enrichment.yaml` | `oil-safety-enrichment.yaml` | Specifies what kind of enrichment (safety data) |

#### Benefits
- **Self-documenting**: File names immediately indicate their purpose
- **Consistent patterns**: Analysis files use `*-analysis.yaml`, discovery uses `*-discovery.yaml`
- **Professional terminology**: Uses industry-standard language
- **Easier onboarding**: New developers can understand the flow from file names

### 2. API Step Names

#### Current → Improved
| Current Step Name | Improved Step Name | Usage Context |
|------------------|-------------------|---------------|
| `'potential-causes'` | `'health-analysis'` | API requests, logging, debugging |
| `'potential-symptoms'` | `'symptom-discovery'` | API requests, logging, debugging |
| `'therapeutic-properties'` | `'healing-properties'` | API requests, logging, debugging |
| `'suggested-oils'` | `'oil-recommendations'` | API requests, logging, debugging |
| `'oil-enrichment'` | `'safety-enrichment'` | API requests, logging, debugging |

#### Benefits
- **Better error messages**: "Failed to complete health-analysis" vs "Failed to complete potential-causes"
- **Clearer logs**: Debug messages become more readable
- **Intuitive debugging**: Developers can immediately understand what step failed

### 3. Hook Names

#### Core Infrastructure Hooks
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `useAIStreaming` | `useSingleStepStreaming` | Clarifies it handles one stream at a time |
| `useParallelStreamingEngine` | `useMultiStepStreaming` | More intuitive than "engine" terminology |

#### Feature-Specific Hook
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `useCreateRecipeStreaming` | `useRecipeStepStreaming` | More generic, could be reused for other features |

#### Hook Method Names
```typescript
// Current methods
startOilSuggestionStreaming()
startOilEnrichmentStreaming()

// Improved methods  
startOilRecommendations()
startSafetyEnrichment()
```

### 4. Component Method Names

```typescript
// Current approach (generic)
const { startStream, startPropertiesStream } = useAIStreaming();

// Improved approach (descriptive)
const {
  startHealthAnalysis,
  startSymptomDiscovery,  
  startHealingPropertiesAnalysis,
  startOilRecommendations,
  startSafetyEnrichment
} = useRecipeStepStreaming();
```

---

## Implementation Strategy

### Phase 1: Backwards Compatible Introduction
**Goal**: Introduce new names without breaking existing functionality

#### 1.1 Create New YAML Files
```
src/features/create-recipe/prompts/
├── health-concern-analysis.yaml      (copy of potential-causes.yaml)
├── symptom-discovery.yaml            (copy of potential-symptoms.yaml)
├── healing-properties-analysis.yaml  (copy of therapeutic-properties.yaml)
├── oil-recommendations.yaml          (copy of suggested-oils.yaml)
├── oil-safety-enrichment.yaml        (copy of oil-enrichment.yaml)
├── potential-causes.yaml             (keep for backwards compatibility)
├── potential-symptoms.yaml           (keep for backwards compatibility)
├── therapeutic-properties.yaml       (keep for backwards compatibility)
├── suggested-oils.yaml               (keep for backwards compatibility)
└── oil-enrichment.yaml               (keep for backwards compatibility)
```

#### 1.2 Update API Route Handler
```typescript
// In src/app/api/ai/streaming/route.ts
const STEP_FILE_MAPPING = {
  // New naming (preferred)
  'health-analysis': 'health-concern-analysis.yaml',
  'symptom-discovery': 'symptom-discovery.yaml',
  'healing-properties': 'healing-properties-analysis.yaml',
  'oil-recommendations': 'oil-recommendations.yaml',
  'safety-enrichment': 'oil-safety-enrichment.yaml',
  
  // Legacy naming (backwards compatibility)
  'potential-causes': 'potential-causes.yaml',
  'potential-symptoms': 'potential-symptoms.yaml',
  'therapeutic-properties': 'therapeutic-properties.yaml',
  'suggested-oils': 'suggested-oils.yaml',
  'oil-enrichment': 'oil-enrichment.yaml'
};
```

#### 1.3 Create Hook Aliases
```typescript
// In src/lib/ai/hooks/index.ts
export { useSingleStepStreaming } from './use-single-step-streaming';
export { useMultiStepStreaming } from './use-multi-step-streaming';

// Backwards compatibility aliases (mark as deprecated)
/** @deprecated Use useSingleStepStreaming instead */
export const useAIStreaming = useSingleStepStreaming;

/** @deprecated Use useMultiStepStreaming instead */
export const useParallelStreamingEngine = useMultiStepStreaming;
```

### Phase 2: Component Migration
**Goal**: Update components one by one to use new naming

#### 2.1 Update Feature-Specific Hook
```typescript
// In src/features/create-recipe/hooks/use-recipe-step-streaming.ts
export function useRecipeStepStreaming() {
  const { streamingState, startStreams, resetState } = useMultiStepStreaming<any>();

  const startOilRecommendations = useCallback(async (
    properties: TherapeuticProperty[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    const requests: ParallelStreamRequest[] = properties.map(property => ({
      id: property.property_id,
      url: '/api/ai/streaming',
      requestData: createStreamRequest(
        'create-recipe',
        'oil-recommendations', // New step name
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        property
      ),
      label: property.property_name,
      responseParser: (updates) => {
        if (updates.finalData?.data?.property_oil_suggestion) {
          return updates.finalData.data.property_oil_suggestion;
        }
        return null;
      }
    }));

    return await startStreams(requests);
  }, [startStreams]);

  const startSafetyEnrichment = useCallback(async (
    propertySuggestions: PropertyOilSuggestions[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    // Implementation with 'safety-enrichment' step name
    const requests: ParallelStreamRequest[] = propertySuggestions.map(suggestion => ({
      id: suggestion.property_id,
      url: '/api/ai/streaming',
      requestData: createStreamRequest(
        'create-recipe',
        'safety-enrichment', // New step name
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        {
          property_id: suggestion.property_id,
          property_name: suggestion.property_name,
          property_name_english: suggestion.property_name_in_english || suggestion.property_name,
          description: suggestion.description,
          suggested_oils: suggestion.suggested_oils || []
        } as TherapeuticProperty
      ),
      label: suggestion.property_name,
      responseParser: (updates) => {
        if (updates.finalData?.output?.data?.enriched_oils) {
          return updates.finalData.output.data.enriched_oils;
        }
        if (updates.finalData?.data?.enriched_oils) {
          return updates.finalData.data.enriched_oils;
        }
        return null;
      }
    }));

    return await startStreams(requests);
  }, [startStreams]);

  return {
    streamingState,
    startOilRecommendations,
    startSafetyEnrichment,
    resetState,
  };
}

// Backwards compatibility export
/** @deprecated Use useRecipeStepStreaming instead */
export const useCreateRecipeStreaming = useRecipeStepStreaming;
```

#### 2.2 Update Components to Use New Step Names
```typescript
// In src/features/create-recipe/components/demographics-form.tsx
const requestData = createStreamRequest(
  'create-recipe',
  'health-analysis', // New step name instead of 'potential-causes'
  healthConcern,
  data,
  [],
  [],
  apiLanguage
);

// In src/features/create-recipe/components/causes-selection.tsx
const requestData = createStreamRequest(
  'create-recipe',
  'symptom-discovery', // New step name instead of 'potential-symptoms'
  healthConcern!,
  demographics!,
  selectedCauses,
  [],
  apiLanguage
);

// In src/features/create-recipe/components/symptoms-selection.tsx
const requestData = createStreamRequest(
  'create-recipe',
  'healing-properties', // New step name instead of 'therapeutic-properties'
  healthConcern!,
  demographics!,
  selectedCauses,
  selectedSymptoms,
  apiLanguage
);
```

### Phase 3: Legacy Cleanup
**Goal**: Remove old files and deprecated aliases

#### 3.1 Remove Old YAML Files
```bash
# After ensuring all components use new step names
rm src/features/create-recipe/prompts/potential-causes.yaml
rm src/features/create-recipe/prompts/potential-symptoms.yaml
rm src/features/create-recipe/prompts/therapeutic-properties.yaml
rm src/features/create-recipe/prompts/suggested-oils.yaml
rm src/features/create-recipe/prompts/oil-enrichment.yaml
```

#### 3.2 Remove Deprecated Aliases
```typescript
// Remove from src/lib/ai/hooks/index.ts
// export const useAIStreaming = useSingleStepStreaming;
// export const useParallelStreamingEngine = useMultiStepStreaming;

// Remove from src/features/create-recipe/hooks/index.ts  
// export const useCreateRecipeStreaming = useRecipeStepStreaming;
```

#### 3.3 Clean Up API Route Handler
```typescript
// Remove legacy mappings from STEP_FILE_MAPPING
const STEP_FILE_MAPPING = {
  'health-analysis': 'health-concern-analysis.yaml',
  'symptom-discovery': 'symptom-discovery.yaml',
  'healing-properties': 'healing-properties-analysis.yaml',
  'oil-recommendations': 'oil-recommendations.yaml',
  'safety-enrichment': 'oil-safety-enrichment.yaml'
};
```

---

## Expected Benefits

### 1. Improved Developer Experience
- **Intuitive understanding**: Step names clearly indicate their purpose
- **Faster debugging**: Error messages become self-explanatory
- **Easier onboarding**: New developers can understand the flow immediately
- **Better code reviews**: Reviewers can quickly understand what code does

### 2. Enhanced Maintainability
- **Self-documenting code**: Method and file names explain their function
- **Consistent patterns**: All similar operations follow the same naming convention
- **Professional terminology**: Uses industry-standard language throughout
- **Reduced cognitive load**: Less mental mapping between technical and business terms

### 3. Better User Experience (Indirect)
- **Clearer error handling**: Better error messages can be shown to users
- **Improved logging**: Support teams can better understand user issues
- **Faster bug fixes**: Developers can identify and fix issues more quickly

---

## Implementation Checklist

### Pre-Implementation
- [ ] Team approval for new naming conventions
- [ ] Backup current codebase
- [ ] Document current functionality for testing

### Phase 1: Backwards Compatible
- [ ] Create new YAML files with improved names (copy existing content)
- [ ] Update API route handler to support both old and new step names
- [ ] Create new hook files with improved names
- [ ] Add deprecated aliases for old hooks
- [ ] Test that existing functionality still works with old names

### Phase 2: Component Migration
- [ ] Update `use-recipe-step-streaming.ts` with new method names
- [ ] Migrate `demographics-form.tsx` to use `'health-analysis'`
- [ ] Migrate `causes-selection.tsx` to use `'symptom-discovery'`
- [ ] Migrate `symptoms-selection.tsx` to use `'healing-properties'`
- [ ] Migrate `properties-display.tsx` to use new hook methods
- [ ] Migrate `therapeutic-properties-table.tsx` to use new hook methods
- [ ] Test each component after migration

### Phase 3: Documentation Updates
- [ ] Update development guide with new naming conventions
- [ ] Update troubleshooting guide with new step names
- [ ] Update all code examples in documentation
- [ ] Update API documentation

### Phase 4: Legacy Cleanup
- [ ] Remove old YAML files
- [ ] Remove deprecated hook aliases
- [ ] Remove legacy step name mappings
- [ ] Final comprehensive testing
- [ ] Update this document with completion status

---

## Risk Assessment

### Low Risk
- **No schema changes**: Only names change, not data structures
- **Backwards compatibility**: Phase 1 maintains full compatibility
- **Gradual migration**: Components updated one at a time
- **Easy rollback**: Can revert to old names at any point

### Medium Risk
- **Team coordination**: Everyone needs to use new conventions consistently
- **Documentation sync**: All docs must be updated together
- **Testing coverage**: Ensure renamed components work correctly

### Mitigation Strategies
- **Comprehensive testing**: Test each component after name changes
- **Clear communication**: Document the migration process for the team
- **Staged deployment**: Deploy phase by phase, not all at once
- **Monitoring**: Watch for any issues during the transition

---

## Alternative Naming Schemes

### Option 2: Process-Focused
```
analyze-health-concern.yaml
discover-symptoms.yaml
identify-healing-properties.yaml
recommend-oils.yaml
enrich-safety-data.yaml
```

### Option 3: Action-Oriented
```
find-root-causes.yaml
explore-symptoms.yaml
match-therapeutic-properties.yaml
suggest-essential-oils.yaml
add-safety-information.yaml
```

**Recommendation**: Use the primary proposal (health-concern-analysis.yaml, etc.) because it:
- Uses consistent naming patterns
- Is professional but readable
- Clearly indicates the purpose
- Follows common naming conventions
- Makes the data flow obvious

---

## Future Considerations

### Extensibility
The improved naming patterns could be applied to:
- Other AI streaming features in the application
- New features that follow similar multi-step patterns
- API naming conventions across the entire application

### Monitoring Success
After implementation, track:
- Developer onboarding time improvements
- Code review feedback on naming clarity
- Bug reports related to naming confusion
- Time spent on debugging and maintenance

---

## Conclusion

This naming improvement plan enhances the Create Recipe feature's maintainability and clarity without changing any underlying functionality or data structures. The phased approach ensures minimal risk while maximizing the benefits of improved naming conventions.

The implementation should be scheduled during a period of lower feature development activity to minimize conflicts and allow for thorough testing of each phase.

---

## References

- Schema change guide: `docs/create-recipe/readme/schema-change-guide.md`
- Current development guide: `docs/create-recipe/readme/development-guide.md`
- Current AI streaming architecture: `docs/create-recipe/readme/ai-streaming-architecture.md` 