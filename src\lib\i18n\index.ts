/**
 * Namespace-based internationalization system
 * Designed for scalable, feature-based translations with AI workflow support
 */

export type SupportedLocale = 'en' | 'pt' | 'es';

export interface TranslationMessages {
  [key: string]: string | TranslationMessages;
}

export interface NamespaceTranslations {
  [namespace: string]: TranslationMessages;
}

// Global translations cache
const translationsCache = new Map<SupportedLocale, NamespaceTranslations>();

// Available namespaces
const NAMESPACES = ['common', 'auth', 'create-recipe', 'dashboard', 'homepage'] as const;

/**
 * Strip metadata keys (keys starting with _) from translation objects
 */
function stripMetadata(obj: any): any {
  if (typeof obj !== 'object' || obj === null) return obj;
  
  const cleaned: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (!key.startsWith('_')) {
      cleaned[key] = stripMetadata(value);
    }
  }
  return cleaned;
}

/**
 * Load all namespace translations for a specific locale
 */
export async function loadNamespacedTranslations(locale: SupportedLocale): Promise<NamespaceTranslations> {
  if (translationsCache.has(locale)) {
    return translationsCache.get(locale)!;
  }

  const translations: NamespaceTranslations = {};
  
  for (const namespace of NAMESPACES) {
    try {
      const module = await import(`./messages/${locale}/${namespace}.json`);
      const content = module.default || module;
      
      // Strip metadata keys (starting with _)
      const cleaned = stripMetadata(content);
      translations[namespace] = cleaned;
    } catch (error) {
      console.warn(`Failed to load namespace '${namespace}' for locale '${locale}', trying English fallback`);
      if (locale !== 'en') {
        try {
          const fallback = await import(`./messages/en/${namespace}.json`);
          translations[namespace] = stripMetadata(fallback.default || fallback);
        } catch (fallbackError) {
          console.error(`Failed to load fallback for namespace '${namespace}':`, fallbackError);
          translations[namespace] = {};
        }
      } else {
        translations[namespace] = {};
      }
    }
  }
  
  translationsCache.set(locale, translations);
  return translations;
}

/**
 * Get nested translation value using dot notation
 */
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Translation function with namespace support
 * @param key - Translation key with namespace syntax: 'namespace:key.path' or legacy 'key.path'
 * @param locale - Target locale
 * @param fallback - Fallback text if translation not found
 * @param variables - Variables to interpolate in the translation
 */
export async function t(
  key: string,
  locale: SupportedLocale = 'en',
  fallback?: string,
  variables?: Record<string, string | number>
): Promise<string> {
  let translation: string | undefined = undefined; // Initialize with undefined

  // Check if key uses namespace syntax
  if (key.includes(':')) {
    const [namespace, ...keyParts] = key.split(':');
    const keyPath = keyParts.join(':');
    
    if (!namespace) return fallback || key;
    
    const namespaceTranslations = await loadNamespacedTranslations(locale);
    
    // Try current locale first
    translation = getNestedValue(namespaceTranslations[namespace] || {}, keyPath);
    
    // Fallback to English if missing and not already English
    if (!translation && locale !== 'en') {
      const englishTranslations = await loadNamespacedTranslations('en');
      translation = getNestedValue(englishTranslations[namespace] || {}, keyPath);
    }
  }
  
  // Final fallback to provided fallback or key
  let finalTranslation = translation || fallback || key;

  // Simple variable interpolation
  if (variables && typeof finalTranslation === 'string') {
    Object.entries(variables).forEach(([varKey, value]) => {
      finalTranslation = finalTranslation.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
    });
  }

  return finalTranslation;
}

/**
 * React hook for translations with namespace support
 */
export function useTranslations(locale: SupportedLocale = 'en') {
  return {
    t: (key: string, fallback?: string, variables?: Record<string, string | number>) => 
      t(key, locale, fallback, variables),
    locale
  };
}

/**
 * Map your existing language codes to our i18n system
 */
export function mapLanguageCode(langCode: string): SupportedLocale {
  const mapping: Record<string, SupportedLocale> = {
    'EN_US': 'en',
    'PT_BR': 'pt', 
    'ES_ES': 'es',
    'en': 'en',
    'pt': 'pt',
    'es': 'es'
  };
  
  return mapping[langCode] || 'en';
} 