/**
 * @fileoverview Demographics Form component for Essential Oil Recipe Creator.
 * Collects user demographics with validation and auto-save functionality.
 * OPTIMIZED: Now uses centralized streaming utilities to eliminate redundant data transformation logic.
 */

'use client';

import React, { useEffect, useMemo, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import { demographicsSchema } from '../schemas/recipe-schemas';
import type { DemographicsData } from '../types/recipe.types';
import { useAIStreaming } from '@/lib/ai/hooks/use-ai-streaming';
import { cn } from '@/lib/utils';
import { AIStreamingModal } from '@/components/ui/ai-streaming-modal';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { useBatchedRecipeUpdates } from '../hooks/use-batched-recipe-updates';
import { useApiLanguage } from '@/lib/i18n/utils';
import { createStreamRequest } from '../utils/api-data-transform';
import { extractFinalStreamingData } from '@/lib/ai/utils/streaming-utils';
import { useI18n } from '@/hooks/use-i18n';
import { RecipeStreamingNavigationButtons } from './recipe-navigation-buttons';

/**
 * Demographics Form component
 */
export function DemographicsForm() {
  const { t } = useI18n();
  const apiLanguage = useApiLanguage();

  /**
   * Age category options (simplified as per user preferences)
   */
  const AGE_CATEGORIES = useMemo(() => [
    { 
      value: 'child', 
      label: t('create-recipe:demographics.ageCategory.options.child.label', 'Child (0-12)'), 
      description: t('create-recipe:demographics.ageCategory.options.child.description', 'Pediatric considerations') 
    },
    { 
      value: 'teen', 
      label: t('create-recipe:demographics.ageCategory.options.teen.label', 'Teen (13-17)'), 
      description: t('create-recipe:demographics.ageCategory.options.teen.description', 'Adolescent development') 
    },
    { 
      value: 'adult', 
      label: t('create-recipe:demographics.ageCategory.options.adult.label', 'Adult (18-64)'), 
      description: t('create-recipe:demographics.ageCategory.options.adult.description', 'General adult population') 
    },
    { 
      value: 'senior', 
      label: t('create-recipe:demographics.ageCategory.options.senior.label', 'Senior (65+)'), 
      description: t('create-recipe:demographics.ageCategory.options.senior.description', 'Elderly considerations') 
    },
  ] as const, [t]);

  /**
   * Gender options (simplified as per user preferences)
   */
  const GENDER_OPTIONS = useMemo(() => [
    { 
      value: 'male', 
      label: t('create-recipe:demographics.gender.options.male', 'Male') 
    },
    { 
      value: 'female', 
      label: t('create-recipe:demographics.gender.options.female', 'Female') 
    },
  ] as const, [t]);

  const {
    healthConcern,
    demographics,
    updateDemographics,
    setPotentialCauses,
    isLoading,
    error,
    setError,
    clearError
  } = useRecipeStore();

  // Get potential causes separately to avoid unnecessary re-renders
  const potentialCauses = useRecipeStore(state => state.potentialCauses);

  const {
    isStreamingCauses,
    streamingError,
    clearStreamingError
  } = useRecipeStore();
  const { goToNext, goToPrevious, canGoNext, canGoPrevious, markCurrentStepCompleted } = useRecipeWizardNavigation();

  // Use batched updates for better performance
  const { completeAIStreaming, startAIStreaming, handleStreamingError } = useBatchedRecipeUpdates();

  // Ref to track if we've already navigated to avoid infinite loops
  const hasNavigatedRef = React.useRef(false);

  // Configure AI streaming hook for potential causes
  const {
    startStream,
    partialData,
    finalData,
    error: streamError,
    isComplete,
    isStreaming: hookIsStreaming
  } = useAIStreaming<any[]>({
    jsonArrayPath: 'data.potential_causes',
    onError: (error) => {
      console.error('Streaming error:', error);
      handleStreamingError(`AI analysis failed: ${error.message}`);
      return false; // Don't retry on error
    }
  });

  // Memoize modal items to prevent unnecessary re-renders
  // FIXED: Use partialData during streaming for progressive updates, fallback to store data
  const modalItems = useMemo(() => {
    // During streaming, use partialData for progressive updates
    const usingPartialData = hookIsStreaming && partialData && Array.isArray(partialData);
    const dataSource = usingPartialData ? partialData : potentialCauses;

    return dataSource.map((cause, index) => ({
      id: `cause-${index}-${cause.cause_id?.slice(0, 10) || 'unknown'}`, // Stable ID based on content
      title: cause.name_localized || `Potential Cause ${index + 1}`,
      subtitle: cause.suggestion_localized || 'Analyzing recommendations...',
      description: cause.explanation_localized || 'Detailed analysis in progress...',
      timestamp: new Date()
    }));
  }, [potentialCauses, partialData, hookIsStreaming]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<DemographicsData>({
    resolver: zodResolver(demographicsSchema),
    defaultValues: {
      gender: demographics?.gender || undefined,
      ageCategory: demographics?.ageCategory || '',
      specificAge: demographics?.specificAge || undefined
    },
    mode: 'onChange'
  });

  const watchedGender = watch('gender');
  const watchedAgeCategory = watch('ageCategory');
  const watchedSpecificAge = watch('specificAge');

  /**
   * Real-time updates to store as user fills out the form
   * This ensures canNavigateToStep always has current data
   * FIXED: Removed 'demographics' from dependencies to prevent infinite loop
   */
  const isUpdatingRef = useRef(false);
  
  useEffect(() => {
    // Prevent updates during programmatic changes to avoid infinite loops
    if (isUpdatingRef.current) return;
    
    // Only update store if all required fields have values
    if (watchedGender && watchedAgeCategory && watchedSpecificAge !== undefined) {
      const currentFormData: DemographicsData = {
        gender: watchedGender,
        ageCategory: watchedAgeCategory,
        specificAge: watchedSpecificAge
      };
      
      // Only update if the data has actually changed to prevent unnecessary re-renders
      const existingData = demographics;
      const hasChanged = !existingData || 
        existingData.gender !== currentFormData.gender ||
        existingData.ageCategory !== currentFormData.ageCategory ||
        existingData.specificAge !== currentFormData.specificAge;
      
      if (hasChanged) {
        console.log('📝 Real-time demographics update:', currentFormData);
        updateDemographics(currentFormData);
      }
    }
  }, [watchedGender, watchedAgeCategory, watchedSpecificAge, updateDemographics]);

  const ageRange = useMemo(() => {
    switch (watchedAgeCategory) {
      case 'child':
        return { min: 0, max: 12 };
      case 'teen':
        return { min: 13, max: 17 };
      case 'adult':
        return { min: 18, max: 64 };
      case 'senior':
        return { min: 65, max: 120 };
      default:
        return { min: 0, max: 120 };
    }
  }, [watchedAgeCategory]);

  useEffect(() => {
    if (watchedAgeCategory) {
      const { min, max } = ageRange;
      if (watchedSpecificAge === undefined || watchedSpecificAge < min || watchedSpecificAge > max) {
        // Set flag to prevent real-time update from triggering
        isUpdatingRef.current = true;
        setValue('specificAge', min, { shouldValidate: true, shouldDirty: true });
        // Reset flag after a brief delay to allow form update to complete
        setTimeout(() => {
          isUpdatingRef.current = false;
        }, 0);
      }
    }
  }, [watchedAgeCategory, watchedSpecificAge, ageRange, setValue]);

  /**
   * Initialize form with existing data
   * FIXED: Use ref to prevent triggering real-time updates during initialization
   */
  const isInitializedRef = useRef(false);
  
  useEffect(() => {
    if (demographics && !isInitializedRef.current) {
      // Set flag to prevent real-time update from triggering during initialization
      isUpdatingRef.current = true;
      setValue('gender', demographics.gender);
      setValue('ageCategory', demographics.ageCategory);
      setValue('specificAge', demographics.specificAge);
      isInitializedRef.current = true;
      
      // Reset flag after initialization is complete
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 0);
    }
  }, [demographics, setValue]);

  /**
   * Handle form submission and initiate AI streaming for potential causes
   * OPTIMIZED: Now uses createStreamRequest utility for consistent request building
   */
  const onSubmit = async (data: DemographicsData) => {
    if (!healthConcern) {
      setError(t('create-recipe:validation.healthConcernRequired', 'Health concern is required to proceed'));
      return;
    }

    try {
      // Save demographics data first
      updateDemographics(data);
      markCurrentStepCompleted();

      // Reset navigation flag for new streaming session
      hasNavigatedRef.current = false;

      // Start AI streaming for potential causes using batched updates
      startAIStreaming('causes');
      clearError();
      clearStreamingError();

      console.log('Starting AI streaming for potential causes...');

      // OPTIMIZED: Use createStreamRequest utility for consistent request building
      // This same pattern will be used by causes step and symptoms step
      const requestData = createStreamRequest(
        'create-recipe',
        'potential-causes',
        healthConcern,
        data, // demographics data
        [], // selectedCauses (empty for this step)
        [], // selectedSymptoms (empty for this step)
        apiLanguage
      );

      await startStream('/api/ai/streaming', requestData);

      console.log('AI streaming initiated successfully');

    } catch (error) {
      console.error('Form submission failed:', error);
      handleStreamingError(`Failed to start analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

    /**
   * Handle streaming partial data updates (for better store sync)
   * SIMPLIFIED: Only log progress during streaming, store update happens on completion
   */
  const lastPartialDataLength = React.useRef(0);
  useEffect(() => {
    if (hookIsStreaming && partialData && Array.isArray(partialData) && partialData.length > 0) {
      // Only update if we have new data (prevent infinite loops)
      if (partialData.length !== lastPartialDataLength.current) {
        console.log('📊 Progressive streaming update:', partialData.length, 'items received');
        lastPartialDataLength.current = partialData.length;
        // Store will be updated only on completion to avoid conflicts
      }
    }
  }, [hookIsStreaming, partialData]);

  /**
   * Handle streaming completion
   * FIXED: Debug finalData structure and use fallback to partialData if needed
   */
  useEffect(() => {
    if (isComplete && !hasNavigatedRef.current) {
      console.log('✅ Streaming completed successfully!');
      // SIMPLIFIED: Only log essential info instead of full data structures
      console.log('📊 Final data available:', !!finalData, 'Partial data items:', Array.isArray(partialData) ? partialData.length : 0);

      try {
        let potentialCauses: any[] = [];

        // Try extracting from finalData first (standard pattern)
        if (finalData) {
          potentialCauses = extractFinalStreamingData(finalData, 'potential_causes');
          console.log('📊 Extracted from finalData:', potentialCauses.length, 'items');
        }

        // Fallback to partialData if finalData extraction failed
        if (potentialCauses.length === 0 && partialData && Array.isArray(partialData)) {
          console.log('📊 Falling back to partialData...');
          potentialCauses = partialData.map(cause => ({
            cause_name: cause.name_localized,
            cause_suggestion: cause.suggestion_localized,  
            explanation: cause.explanation_localized,
            cause_id: cause.cause_id
          }));
          console.log('📊 Extracted from partialData:', potentialCauses.length, 'items');
        }

        if (potentialCauses.length > 0) {
          setPotentialCauses(potentialCauses);
          completeAIStreaming('causes', potentialCauses);

          // Navigate to next step only once
          hasNavigatedRef.current = true;
          if (canGoNext()) {
            console.log('🚀 Navigating to next step...');
            goToNext();
          }
        } else {
          handleStreamingError('No potential causes found in the analysis');
        }
      } catch (error) {
        console.error('Error processing streaming completion:', error);
        handleStreamingError(`Failed to process analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }, [isComplete, finalData, partialData, setPotentialCauses, completeAIStreaming, canGoNext, goToNext, handleStreamingError]);

  /**
   * Handle streaming errors
   */
  useEffect(() => {
    if (streamError) {
      handleStreamingError(`AI analysis failed: ${streamError}`);
    }
  }, [streamError, handleStreamingError]);

  /**
   * Handle continue to next step
   */
  const handleContinue = async () => {
    if (isValid) {
      const data = {
        gender: watchedGender,
        ageCategory: watchedAgeCategory,
        specificAge: watchedSpecificAge
      };
      await onSubmit(data);
    }
  };

  /**
   * Handle go back
   */
  const handleGoBack = async () => {
    if (canGoPrevious()) {
      await goToPrevious();
    }
  };

  const safeAgeForSlider = (typeof watchedSpecificAge === 'number' && !isNaN(watchedSpecificAge)) ? watchedSpecificAge : ageRange.min;
  const displayAge = (typeof watchedSpecificAge === 'number' && !isNaN(watchedSpecificAge)) ? watchedSpecificAge.toString() : '';

  /**
   * Handle age increment
   */
  const handleAgeIncrement = () => {
    const currentAge = watchedSpecificAge || ageRange.min;
    const newAge = Math.min(currentAge + 1, ageRange.max);
    setValue('specificAge', newAge, { shouldValidate: true, shouldDirty: true });
  };

  /**
   * Handle age decrement
   */
  const handleAgeDecrement = () => {
    const currentAge = watchedSpecificAge || ageRange.min;
    const newAge = Math.max(currentAge - 1, ageRange.min);
    setValue('specificAge', newAge, { shouldValidate: true, shouldDirty: true });
  };

  return (
    <div data-testid="demographics-form" className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-foreground">
          {t('create-recipe:demographics.title', 'Tell us about yourself')}
        </h2>
        <p className="text-muted-foreground">
          {t('create-recipe:demographics.description', 'This information helps us provide more personalized essential oil recommendations based on your demographics.')}
        </p>
      </div>

      {/* Error Display */}
      {(error || streamingError) && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-destructive text-sm">{error || streamingError}</p>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Gender Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-foreground">
            {t('create-recipe:demographics.gender.label', 'Gender')} *
          </label>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {GENDER_OPTIONS.map((option) => (
              <label
                key={option.value}
                className={cn(
                  "flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors",
                  "hover:bg-muted/50",
                  watchedGender === option.value
                    ? "border-primary bg-primary/5"
                    : "border-input"
                )}
              >
                <input
                  type="radio"
                  {...register('gender')}
                  value={option.value}
                  className="w-4 h-4 text-primary focus:ring-primary"
                  disabled={isLoading}
                />
                <span className="text-sm font-medium">{option.label}</span>
              </label>
            ))}
          </div>

          {errors.gender && (
            <p className="text-destructive text-sm">{errors.gender.message}</p>
          )}
        </div>

        {/* Age Category Selection - Only show after gender is selected */}
        {watchedGender && (
          <div className="space-y-3 animate-in fade-in-0 slide-in-from-top-2 duration-300">
            <label className="text-sm font-medium text-foreground">
              {t('create-recipe:demographics.ageCategory.label', 'Age Category')} *
            </label>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {AGE_CATEGORIES.map((category) => (
                <label
                  key={category.value}
                  className={cn(
                    "flex flex-col space-y-1 p-4 border rounded-lg cursor-pointer transition-colors",
                    "hover:bg-muted/50",
                    watchedAgeCategory === category.value
                      ? "border-primary bg-primary/5"
                      : "border-input"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      {...register('ageCategory')}
                      value={category.value}
                      className="w-4 h-4 text-primary focus:ring-primary"
                      disabled={isLoading}
                    />
                    <span className="text-sm font-medium">{category.label}</span>
                  </div>
                  <span className="text-xs text-muted-foreground ml-7">
                    {category.description}
                  </span>
                </label>
              ))}
            </div>

            {errors.ageCategory && (
              <p className="text-destructive text-sm">{errors.ageCategory.message}</p>
            )}
          </div>
        )}

        {/* Specific Age Input - Only show after age category is selected */}
        {watchedGender && watchedAgeCategory && (
          <div className="space-y-4 animate-in fade-in-0 slide-in-from-top-2 duration-300">
            <div className="flex justify-between items-center">
              <label htmlFor="specificAge" className="text-sm font-medium text-foreground">
                {t('create-recipe:demographics.specificAge.label', 'Specific Age: {age}', { age: displayAge })} *
              </label>
            </div>
            
            {/* Age Controls with + and - buttons */}
            <div className="space-y-4">
                           {/* Button Controls Row */}
               <div className="flex items-center justify-center gap-4">
                 <Button
                   type="button"
                   variant="outline"
                   onClick={handleAgeDecrement}
                   disabled={isLoading || !watchedAgeCategory || (watchedSpecificAge || ageRange.min) <= ageRange.min}
                   className="size-7 rounded-full border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground"
                 >
                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-minus">
                     <path d="M5 12h14"></path>
                   </svg>
                   <span className="sr-only">Decrease</span>
                 </Button>
                 
                 <div className="text-center">
                   <div className="text-4xl font-bold tracking-tighter tabular-nums">
                     {displayAge || ageRange.min}
                   </div>
                   <div className="text-muted-foreground text-xs lowercase">
                     {t('create-recipe:demographics.specificAge.unit', 'Years old')}
                   </div>
                 </div>
                 
                 <Button
                   type="button"
                   variant="outline"
                   onClick={handleAgeIncrement}
                   disabled={isLoading || !watchedAgeCategory || (watchedSpecificAge || ageRange.min) >= ageRange.max}
                   className="size-7 rounded-full border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground"
                 >
                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plus">
                     <path d="M5 12h14"></path>
                     <path d="M12 5v14"></path>
                   </svg>
                   <span className="sr-only">Increase</span>
                 </Button>
               </div>
               
               {/* Hidden input for form validation */}
               <input
                 id="specificAge"
                 type="number"
                 {...register('specificAge', { valueAsNumber: true })}
                 value={watchedSpecificAge || ''}
                 className="sr-only"
                 tabIndex={-1}
                 readOnly
               />
              
              {/* Slider Row */}
              <div className="px-4">
                <Slider
                  id="age-slider"
                  min={ageRange.min}
                  max={ageRange.max}
                  step={1}
                  value={[safeAgeForSlider]}
                  onValueChange={(value) => setValue('specificAge', value[0] || ageRange.min, { shouldValidate: true, shouldDirty: true })}
                  disabled={isLoading || !watchedAgeCategory}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{ageRange.min}</span>
                  <span>{ageRange.max}</span>
                </div>
              </div>
            </div>
            
            {errors.specificAge && (
              <p className="text-destructive text-sm">{errors.specificAge.message}</p>
            )}
                       <p className="text-xs text-muted-foreground text-center">
               {t('create-recipe:demographics.specificAge.instructions.withCategory', 'Use the + and - buttons or the slider to adjust your age.')}
             </p>
          </div>
        )}

        {/* Navigation Buttons - Only show after all fields are completed */}
        {watchedGender && watchedAgeCategory && watchedSpecificAge && (
          <div className="animate-in fade-in-0 slide-in-from-top-2 duration-300">
            <RecipeStreamingNavigationButtons
              onPrevious={handleGoBack}
              onNext={handleContinue}
              canGoPrevious={canGoPrevious()}
              canGoNext={canGoNext()}
              isValid={Boolean(watchedGender && watchedAgeCategory && watchedSpecificAge !== undefined)}
              isLoading={isLoading}
              isStreaming={isStreamingCauses}
              previousLabel={t('common.buttons.previous', 'Previous')}
              nextLabel={t('common.buttons.continue', 'Continue')}
              statusMessage={t('create-recipe:common.readyToContinue', 'Ready to continue')}
              streamingMessage={t('create-recipe:streaming.analyzing', 'Analyzing your information...')}
            />
          </div>
        )}
      </form>

      {/* AI Streaming Modal */}
      <AIStreamingModal
        isOpen={hookIsStreaming}
        title={t('create-recipe:streaming.analyzing', 'Analyzing your information...')}
        description={t('create-recipe:streaming.analyzingDescription', 'We are analyzing your demographics and health concern to identify potential causes and provide personalized recommendations.')}
        items={modalItems}
        onClose={() => {
          // Modal cannot be closed during streaming
        }}
      />
    </div>
  );
}
