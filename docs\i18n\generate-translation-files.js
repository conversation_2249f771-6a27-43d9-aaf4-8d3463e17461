#!/usr/bin/env node

/**
 * <PERSON>ript to generate AI-friendly translation files
 * Usage: node scripts/generate-translation-files.js
 */

const fs = require('fs');
const path = require('path');

const MESSAGES_DIR = path.join(__dirname, '../src/lib/i18n/messages');
const BASE_FILE = path.join(MESSAGES_DIR, 'en.json');

// Language configurations
const LANGUAGES = {
  pt: {
    name: 'Portuguese (Brazil)',
    context: 'Essential oils and aromatherapy web application for Brazilian Portuguese speakers',
    tone: 'Professional but friendly, health-focused',
    notes: 'Maintain medical accuracy. Use Brazilian Portuguese terms. Consider cultural context for wellness and health terminology.'
  },
  es: {
    name: 'Spanish (Latin America)', 
    context: 'Essential oils and aromatherapy web application for Latin American Spanish speakers',
    tone: 'Professional but friendly, health-focused',
    notes: 'Maintain medical accuracy. Use Latin American Spanish terms. Consider cultural context for wellness and health terminology.'
  }
};

/**
 * Add "TRANSLATE:" prefix to all string values for AI translation
 */
function addTranslatePrefix(obj, path = '') {
  if (typeof obj === 'string') {
    return `TRANSLATE: ${obj}`;
  }
  
  if (Array.isArray(obj)) {
    return obj.map((item, index) => addTranslatePrefix(item, `${path}[${index}]`));
  }
  
  if (typeof obj === 'object' && obj !== null) {
    const result = {};
    Object.keys(obj).forEach(key => {
      // Skip special keys
      if (key.startsWith('_')) {
        result[key] = obj[key];
        return;
      }
      
      const newPath = path ? `${path}.${key}` : key;
      result[key] = addTranslatePrefix(obj[key], newPath);
    });
    return result;
  }
  
  return obj;
}

/**
 * Generate translation file for a specific language
 */
function generateTranslationFile(langCode, config) {
  try {
    // Read the base English file
    const baseContent = fs.readFileSync(BASE_FILE, 'utf8');
    const baseTranslations = JSON.parse(baseContent);
    
    // Create the translation template
    const translationTemplate = {
      _ai_translation_instructions: {
        target_language: config.name,
        context: config.context,
        tone: config.tone,
        notes: config.notes,
        instructions: [
          "Replace all 'TRANSLATE: ' prefixes with the actual translation",
          "Maintain the same JSON structure and keys",
          "Keep variable placeholders like {current}, {total} unchanged",
          "Ensure translations are culturally appropriate",
          "Maintain medical and wellness terminology accuracy"
        ]
      },
      ...addTranslatePrefix(baseTranslations)
    };
    
    // Write the translation file
    const outputFile = path.join(MESSAGES_DIR, `${langCode}.json`);
    fs.writeFileSync(outputFile, JSON.stringify(translationTemplate, null, 2));
    
    console.log(`✅ Generated translation template: ${outputFile}`);
    console.log(`📝 Ready to send to AI for ${config.name} translation`);
    
  } catch (error) {
    console.error(`❌ Error generating ${langCode} translation file:`, error.message);
  }
}

/**
 * Main function
 */
function main() {
  console.log('🌍 Generating AI-friendly translation files...\n');
  
  // Ensure messages directory exists
  if (!fs.existsSync(MESSAGES_DIR)) {
    fs.mkdirSync(MESSAGES_DIR, { recursive: true });
    console.log('📁 Created messages directory');
  }
  
  // Check if base English file exists
  if (!fs.existsSync(BASE_FILE)) {
    console.error('❌ Base English file not found:', BASE_FILE);
    console.log('💡 Please create the English translation file first');
    process.exit(1);
  }
  
  // Generate translation files for each language
  Object.entries(LANGUAGES).forEach(([langCode, config]) => {
    generateTranslationFile(langCode, config);
  });
  
  console.log(`\n🎉 Translation files generated successfully!`);
  console.log(`\n📋 Next steps:`);
  console.log(`1. Send the generated .json files to your AI translator`);
  console.log(`2. Ask AI to replace all "TRANSLATE: " prefixes with actual translations`);
  console.log(`3. Review and validate the translations`);
  console.log(`4. Test in your application`);
}

// Run the script
if (require.main === module) {
  main();
} 