# Schema and Naming Improvements: Future Implementation Plan

## Overview

This document outlines a comprehensive plan to improve the naming conventions and schema organization of the Create Recipe feature's AI streaming system. The current naming scheme, while functional, could be more intuitive and maintainable for long-term development.

## Current Context (As of Implementation Date)

### Current Architecture
The Create Recipe feature uses a **multi-streaming architecture** with different approaches for different steps:

1. **Single-Stream Steps** (Demographics, Causes, Symptoms) use `useAIStreaming`
2. **Parallel-Stream Steps** (Oil Suggestions, Oil Enrichment) use `useCreateRecipeStreaming` → `useParallelStreamingEngine`

### Current File Structure
```
src/features/create-recipe/prompts/
├── potential-causes.yaml
├── potential-symptoms.yaml  
├── therapeutic-properties.yaml
├── suggested-oils.yaml
└── oil-enrichment.yaml

src/lib/ai/hooks/
├── use-ai-streaming.ts
└── use-parallel-streaming-engine.ts

src/features/create-recipe/hooks/
└── use-create-recipe-streaming.ts
```

### Current Data Flow
```
YAML Schema → AI Response → Response Parser → Zustand Store → React Components → User UI
```

### Current Step Mapping
| Step | Component | Hook | YAML File | Purpose |
|------|-----------|------|-----------|---------|
| Demographics | `demographics-form.tsx` | `useAIStreaming` | `potential-causes.yaml` | Analyze health concern → root causes |
| Causes | `causes-selection.tsx` | `useAIStreaming` | `potential-symptoms.yaml` | Find related symptoms |
| Symptoms | `symptoms-selection.tsx` | `useAIStreaming` | `therapeutic-properties.yaml` | Identify healing properties |
| Properties | `properties-display.tsx` | `useCreateRecipeStreaming` | `suggested-oils.yaml` | Recommend essential oils |
| Enrichment | `therapeutic-properties-table.tsx` | `useCreateRecipeStreaming` | `oil-enrichment.yaml` | Add safety information |

---

## Proposed Improvements

### 1. YAML Prompt File Renaming

#### Current → Improved Mapping
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `potential-causes.yaml` | `health-concern-analysis.yaml` | More descriptive of the actual analysis process |
| `potential-symptoms.yaml` | `symptom-discovery.yaml` | Clearer purpose - discovering related symptoms |
| `therapeutic-properties.yaml` | `healing-properties-analysis.yaml` | More human-readable and specific |
| `suggested-oils.yaml` | `oil-recommendations.yaml` | "Recommendations" is clearer than "suggestions" |
| `oil-enrichment.yaml` | `oil-safety-enrichment.yaml` | Specifies what kind of enrichment (safety data) |

#### Benefits
- **Self-documenting**: File names immediately indicate their purpose
- **Consistent patterns**: Analysis files use `*-analysis.yaml`, discovery uses `*-discovery.yaml`
- **Professional terminology**: Uses industry-standard language
- **Reduces cognitive load**: New developers can understand the flow from file names alone

### 2. Hook Renaming Strategy

#### Core Infrastructure Hooks
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `useAIStreaming` | `useSingleStepStreaming` | Clarifies it handles one stream at a time |
| `useParallelStreamingEngine` | `useMultiStepStreaming` | More intuitive than "engine" terminology |

#### Feature-Specific Hook
| Current Name | Improved Name | Reasoning |
|-------------|---------------|-----------|
| `useCreateRecipeStreaming` | `useRecipeStepStreaming` | More generic, could be reused for other features |

#### Method Names Within Hooks
```typescript
// Current methods
startOilSuggestionStreaming()
startOilEnrichmentStreaming()

// Improved methods  
startOilRecommendations()
startSafetyEnrichment()
```

### 3. API Step Names

#### Current → Improved Step Identifiers
| Current Step Name | Improved Step Name | Usage Context |
|------------------|-------------------|---------------|
| `'potential-causes'` | `'health-analysis'` | API requests, logging, debugging |
| `'potential-symptoms'` | `'symptom-discovery'` | API requests, logging, debugging |
| `'therapeutic-properties'` | `'healing-properties'` | API requests, logging, debugging |
| `'suggested-oils'` | `'oil-recommendations'` | API requests, logging, debugging |
| `'oil-enrichment'` | `'safety-enrichment'` | API requests, logging, debugging |

### 4. Component Method Naming

```typescript
// Current approach (generic)
const { startStream, startPropertiesStream } = useAIStreaming();

// Improved approach (descriptive)
const {
  startHealthAnalysis,
  startSymptomDiscovery,  
  startHealingPropertiesAnalysis,
  startOilRecommendations,
  startSafetyEnrichment
} = useRecipeStepStreaming();
```

---

## Implementation Strategy

### Phase 1: Backwards Compatible Introduction
**Goal**: Introduce new names without breaking existing functionality

#### 1.1 Create Hook Aliases
```typescript
// In src/lib/ai/hooks/index.ts
export { useSingleStepStreaming } from './use-single-step-streaming';
export { useMultiStepStreaming } from './use-multi-step-streaming';

// Backwards compatibility aliases (mark as deprecated)
/** @deprecated Use useSingleStepStreaming instead */
export const useAIStreaming = useSingleStepStreaming;

/** @deprecated Use useMultiStepStreaming instead */
export const useParallelStreamingEngine = useMultiStepStreaming;
```

#### 1.2 Create New YAML Files
```
src/features/create-recipe/prompts/
├── health-concern-analysis.yaml      (copy of potential-causes.yaml)
├── symptom-discovery.yaml            (copy of potential-symptoms.yaml)
├── healing-properties-analysis.yaml  (copy of therapeutic-properties.yaml)
├── oil-recommendations.yaml          (copy of suggested-oils.yaml)
├── oil-safety-enrichment.yaml        (copy of oil-enrichment.yaml)
├── potential-causes.yaml             (keep for backwards compatibility)
├── potential-symptoms.yaml           (keep for backwards compatibility)
├── therapeutic-properties.yaml       (keep for backwards compatibility)
├── suggested-oils.yaml               (keep for backwards compatibility)
└── oil-enrichment.yaml               (keep for backwards compatibility)
```

#### 1.3 Update API Route Handler
```typescript
// In src/app/api/ai/streaming/route.ts
const STEP_FILE_MAPPING = {
  // New naming (preferred)
  'health-analysis': 'health-concern-analysis.yaml',
  'symptom-discovery': 'symptom-discovery.yaml',
  'healing-properties': 'healing-properties-analysis.yaml',
  'oil-recommendations': 'oil-recommendations.yaml',
  'safety-enrichment': 'oil-safety-enrichment.yaml',
  
  // Legacy naming (backwards compatibility)
  'potential-causes': 'potential-causes.yaml',
  'potential-symptoms': 'potential-symptoms.yaml',
  'therapeutic-properties': 'therapeutic-properties.yaml',
  'suggested-oils': 'suggested-oils.yaml',
  'oil-enrichment': 'oil-enrichment.yaml'
};
```

### Phase 2: Gradual Component Migration
**Goal**: Update components one by one to use new naming

#### 2.1 Update Feature-Specific Hook
```typescript
// In src/features/create-recipe/hooks/use-recipe-step-streaming.ts
export function useRecipeStepStreaming() {
  const { streamingState, startStreams, resetState } = useMultiStepStreaming<any>();

  const startOilRecommendations = useCallback(async (
    properties: TherapeuticProperty[],
    // ... other params
  ) => {
    const requests: ParallelStreamRequest[] = properties.map(property => ({
      id: property.property_id,
      url: '/api/ai/streaming',
      requestData: createStreamRequest(
        'create-recipe',
        'oil-recommendations', // New step name
        // ... other params
      ),
      label: property.property_name,
      responseParser: (updates) => {
        if (updates.finalData?.data?.property_oil_suggestion) {
          return updates.finalData.data.property_oil_suggestion;
        }
        return null;
      }
    }));

    return await startStreams(requests);
  }, [startStreams]);

  const startSafetyEnrichment = useCallback(async (
    propertySuggestions: PropertyOilSuggestions[],
    // ... other params
  ) => {
    // Similar implementation with 'safety-enrichment' step name
  }, [startStreams]);

  return {
    streamingState,
    startOilRecommendations,
    startSafetyEnrichment,
    resetState,
  };
}

// Backwards compatibility export
/** @deprecated Use useRecipeStepStreaming instead */
export const useCreateRecipeStreaming = useRecipeStepStreaming;
```

#### 2.2 Update Components Incrementally
```typescript
// In src/features/create-recipe/components/demographics-form.tsx
const {
  startStream: startHealthAnalysis, // Rename for clarity
  partialData,
  finalData,
  // ... other properties
} = useSingleStepStreaming<any[]>({
  jsonArrayPath: 'data.potential_causes',
  // ... other config
});

// Update request call
const requestData = createStreamRequest(
  'create-recipe',
  'health-analysis', // New step name
  healthConcern,
  data,
  [],
  [],
  apiLanguage
);

await startHealthAnalysis('/api/ai/streaming', requestData);
```

### Phase 3: Schema Documentation Update
**Goal**: Update all documentation to reflect new naming

#### 3.1 Update Schema Change Guide
```markdown
# Updated mapping table in docs/create-recipe/readme/schema-change-guide.md

| Step | Component | Streaming Hook | Schema File | JSON Path | Response Parser |
|------|-----------|----------------|-------------|-----------|-----------------|
| **Health Analysis** | `demographics-form.tsx` | `useSingleStepStreaming` | `health-concern-analysis.yaml` | `data.potential_causes` | `extractFinalStreamingData()` |
| **Symptom Discovery** | `causes-selection.tsx` | `useSingleStepStreaming` | `symptom-discovery.yaml` | `data.potential_symptoms` | `extractFinalStreamingData()` |
| **Healing Properties** | `symptoms-selection.tsx` | `useSingleStepStreaming` | `healing-properties-analysis.yaml` | `data.therapeutic_properties` | `extractFinalStreamingData()` |
| **Oil Recommendations** | `properties-display.tsx` | `useRecipeStepStreaming` | `oil-recommendations.yaml` | N/A | `updates.finalData?.data?.property_oil_suggestion` |
| **Safety Enrichment** | `therapeutic-properties-table.tsx` | `useRecipeStepStreaming` | `oil-safety-enrichment.yaml` | N/A | `updates.finalData?.output?.data?.enriched_oils` |
```

#### 3.2 Update All Code Examples
Update all documentation files to use the new naming convention in code examples.

### Phase 4: Legacy Cleanup
**Goal**: Remove old files and deprecated aliases

#### 4.1 Remove Old YAML Files
```bash
# After ensuring all components use new step names
rm src/features/create-recipe/prompts/potential-causes.yaml
rm src/features/create-recipe/prompts/potential-symptoms.yaml
rm src/features/create-recipe/prompts/therapeutic-properties.yaml
rm src/features/create-recipe/prompts/suggested-oils.yaml
rm src/features/create-recipe/prompts/oil-enrichment.yaml
```

#### 4.2 Remove Deprecated Aliases
```typescript
// Remove from src/lib/ai/hooks/index.ts
// export const useAIStreaming = useSingleStepStreaming;
// export const useParallelStreamingEngine = useMultiStepStreaming;

// Remove from src/features/create-recipe/hooks/index.ts  
// export const useCreateRecipeStreaming = useRecipeStepStreaming;
```

#### 4.3 Update API Route Handler
```typescript
// Remove legacy mappings from STEP_FILE_MAPPING
const STEP_FILE_MAPPING = {
  'health-analysis': 'health-concern-analysis.yaml',
  'symptom-discovery': 'symptom-discovery.yaml',
  'healing-properties': 'healing-properties-analysis.yaml',
  'oil-recommendations': 'oil-recommendations.yaml',
  'safety-enrichment': 'oil-safety-enrichment.yaml'
};
```

---

## Expected Benefits

### 1. Developer Experience Improvements
- **Faster onboarding**: New developers can understand the system flow from file names
- **Reduced confusion**: Clear distinction between single-stream and multi-stream approaches
- **Better debugging**: Error messages and logs will be more descriptive
- **Easier maintenance**: Schema changes become more obvious

### 2. Code Quality Improvements
- **Self-documenting code**: Method names clearly indicate their purpose
- **Consistent patterns**: All similar operations follow the same naming convention
- **Professional terminology**: Uses industry-standard language throughout
- **Reduced cognitive load**: Less mental mapping between technical and business concepts

### 3. Long-term Maintainability
- **Scalability**: New steps can follow established naming patterns
- **Flexibility**: Generic hook names allow reuse in other features
- **Documentation**: Easier to write and maintain technical documentation
- **Testing**: Test names become more descriptive and meaningful

---

## Risk Assessment

### Low Risk
- **Backwards compatibility**: Phase 1 maintains full compatibility
- **Gradual migration**: Components can be updated one at a time
- **Rollback capability**: Old files remain until migration is complete

### Medium Risk
- **Developer coordination**: Team needs to agree on new conventions
- **Documentation updates**: All docs need to be updated consistently
- **Testing coverage**: Ensure all renamed components still work correctly

### Mitigation Strategies
- **Comprehensive testing**: Test each component after migration
- **Staged deployment**: Deploy phase by phase, not all at once
- **Clear communication**: Document the migration process for the team
- **Monitoring**: Watch for any issues during the transition period

---

## Implementation Checklist

### Pre-Implementation
- [ ] Team approval for new naming conventions
- [ ] Backup current codebase
- [ ] Create comprehensive test suite for existing functionality

### Phase 1: Backwards Compatible
- [ ] Create new hook files with improved names
- [ ] Add deprecated aliases for old hooks
- [ ] Create new YAML files with improved names
- [ ] Update API route handler to support both old and new step names
- [ ] Test that existing functionality still works

### Phase 2: Component Migration
- [ ] Update `use-recipe-step-streaming.ts` with new method names
- [ ] Migrate `demographics-form.tsx` to use new naming
- [ ] Migrate `causes-selection.tsx` to use new naming
- [ ] Migrate `symptoms-selection.tsx` to use new naming
- [ ] Migrate `properties-display.tsx` to use new naming
- [ ] Migrate `therapeutic-properties-table.tsx` to use new naming
- [ ] Test each component after migration

### Phase 3: Documentation
- [ ] Update schema change guide
- [ ] Update development guide
- [ ] Update troubleshooting guide
- [ ] Update all code examples in documentation
- [ ] Update API documentation

### Phase 4: Cleanup
- [ ] Remove old YAML files
- [ ] Remove deprecated hook aliases
- [ ] Remove legacy step name mappings
- [ ] Final comprehensive testing
- [ ] Update this document with completion status

---

## Future Considerations

### Additional Naming Improvements
Based on future feature development, consider:
- **Component naming**: Align component names with new step names
- **Store action naming**: Update Zustand store actions to match new conventions
- **Type naming**: Consider updating TypeScript interface names for consistency

### Extension to Other Features
The improved naming patterns established here could be applied to:
- Other AI streaming features in the application
- New features that follow similar multi-step patterns
- API naming conventions across the entire application

### Monitoring and Metrics
After implementation, consider tracking:
- Developer onboarding time improvements
- Code review feedback on naming clarity
- Bug reports related to naming confusion
- Time spent on maintenance tasks

---

## Conclusion

This naming improvement plan provides a clear path to enhance the maintainability and clarity of the Create Recipe feature's AI streaming system. The phased approach ensures minimal risk while maximizing the benefits of improved naming conventions.

The implementation should be scheduled during a period of lower feature development activity to minimize conflicts and allow for thorough testing of each phase.

---

## References

- Current schema change guide: `docs/create-recipe/readme/schema-change-guide.md`
- Current development guide: `docs/create-recipe/readme/development-guide.md`
- Current AI streaming architecture: `docs/create-recipe/readme/ai-streaming-architecture.md` 