/**
 * Common types and utilities for AI streaming functionality
 * Shared between use-ai-streaming and use-ai-parallel-streaming hooks
 * MAINTAINS 100% COMPATIBILITY with current working implementation
 */

import { parse } from 'best-effort-json-parser';

/**
 * Stream event types for AI streaming (matches current codebase)
 */
export interface StreamEvent {
  type: 'text_chunk' | 'structured_data' | 'structured_complete' | 'completion' | 'error';
  content?: string;
  data?: any;
  message?: string;
}

/**
 * Base request structure for streaming API calls (matches current codebase)
 */
export interface StreamRequest {
  feature: string;
  step: string;
  data: any;
}

/**
 * Streaming state interface (matches current codebase)
 */
export interface StreamingState<T = any> {
  streamingText: string;
  partialData: T | null;
  isStreaming: boolean;
  isComplete: boolean;
  error: string | null;
  finalData: T | null;
}

/**
 * Configuration options for streaming
 */
export interface StreamConfig {
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Delay between retries in ms */
  retryDelay?: number;
  /** Request timeout in ms */
  timeout?: number;
  /** Error handler callback */
  onError?: (error: Error, retryCount: number) => boolean | Promise<boolean>;
  /** Path to array in JSON response */
  jsonArrayPath?: string;
}

/**
 * Default streaming configuration
 */
export const DEFAULT_STREAM_CONFIG: Required<StreamConfig> = {
  maxRetries: 3,
  retryDelay: 1000,
  timeout: 90000, // Increased to 90 seconds for complex AI analysis
  onError: () => true,
  jsonArrayPath: ''
};

/**
 * EXACT REPLICA: Processes streaming text with jsonArrayPath support
 * Maintains exact compatibility with current hook's best-effort parsing
 */
export function processStreamingText<T>(
  newContent: string,
  currentText: string,
  jsonArrayPath: string
): { updatedText: string; partialData: T | null } {
  const updatedText = currentText + newContent;
  let partialData: T | null = null;

  if (jsonArrayPath) {
    try {
      const parsed = parse(updatedText);
      const get = (p: string, o: any) =>
        p.split('.').reduce((xs, x) => (xs && xs[x] ? xs[x] : null), o);
      const partialArray = get(jsonArrayPath, parsed);

      if (Array.isArray(partialArray)) {
        partialData = partialArray as T;
      }
    } catch (e) {
      // Best-effort parser can fail on intermediate chunks, ignore (EXACT replica)
    }
  }

  return { updatedText, partialData };
}

/**
 * EXACT REPLICA: Sophisticated structured data validation from current hook
 * Maintains exact compatibility with current validation logic
 */
export function validateStructuredData(data: any): boolean {
  // Generic validation - check if item has meaningful string content
  return Object.values(data).some(
    value => typeof value === 'string' && value.length > 3 && !value.endsWith('...')
  );
}

/**
 * EXACT REPLICA: Process structured data with array building logic
 * Maintains exact compatibility with current hook's array building
 */
export function processStructuredData<T>(
  newData: any,
  currentPartialData: T | null,
  debugInfo?: { index?: any; dataType?: any; itemKeys?: string[] }
): T | null {
  const prevArray = Array.isArray(currentPartialData) ? currentPartialData : [];
  
  console.log('🔍 HOOK DEBUG - Current partialData before update:', prevArray.length, 'items');

  const hasRequiredContent = validateStructuredData(newData);

  if (hasRequiredContent) {
    const newArray = [...prevArray, newData];
    console.log('✅ HOOK DEBUG - Added complete item, total items:', newArray.length);
    return newArray as T;
  } else {
    console.log('⏳ HOOK DEBUG - Skipping incomplete item - insufficient content');
    return prevArray as T;
  }
}

/**
 * Processes a stream response chunk and updates state
 * Compatible with current implementation patterns
 */
export function processStreamChunk<T>(
  chunk: string,
  currentState: StreamingState<T>,
  jsonArrayPath?: string
): Partial<StreamingState<T>> {
  const lines = chunk.split('\n').filter(line => line.trim());
  const updates: Partial<StreamingState<T>> = {};
  
  for (const line of lines) {
    if (!line.startsWith('data: ')) continue;
    
    try {
      const data = JSON.parse(line.slice(6));
      
      if (data.type === 'text_chunk' && data.content) {
        updates.streamingText = (currentState.streamingText || '') + data.content;
      } 
      else if (data.type === 'structured_data' && data.data) {
        updates.partialData = data.data;
      }
      else if (data.type === 'structured_complete' && data.data) {
        updates.finalData = data.data;
        updates.isComplete = true;
      }
      else if (data.type === 'error' && data.message) {
        updates.error = data.message;
        updates.isComplete = true;
      }
    } catch (error) {
      console.warn('Failed to parse stream data:', error);
    }
  }
  
  return updates;
}

/**
 * Validates a stream request structure
 */
export function validateStreamRequest(request: StreamRequest): boolean {
  return !!(
    request &&
    typeof request.feature === 'string' &&
    typeof request.step === 'string' &&
    request.data
  );
}

/**
 * Creates a timeout promise for stream operations
 */
export function createTimeoutPromise(timeoutMs: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Stream timeout after ${timeoutMs}ms`)), timeoutMs);
  });
}

/**
 * EXACT REPLICA: Helper function for nested object access (from current hook)
 */
export function getNestedProperty(path: string, obj: any): any {
  return path.split('.').reduce((xs, x) => (xs && xs[x] ? xs[x] : null), obj);
}

/**
 * Transforms recipe-wizard format data to create-recipe format
 * Eliminates redundant transformation logic in components
 * @param data Raw data from AI streaming (recipe-wizard format)
 * @param stepType The type of step ('causes', 'symptoms', 'properties', etc.)
 * @returns Transformed data in create-recipe format
 */
export function transformRecipeWizardData(data: any[], stepType: 'causes' | 'symptoms' | 'properties'): any[] {
  if (!Array.isArray(data) || data.length === 0) {
    return [];
  }

  console.log('🔄 Transforming recipe-wizard data:', data.length, 'items for step:', stepType);

  // Filter complete items using our existing validation
  const completeItems = data.filter(item => validateStructuredData(item));
  
  console.log('✅ Complete items found:', completeItems.length, 'of', data.length);

  // Transform based on step type
  switch (stepType) {
    case 'causes':
      return completeItems.map((cause: any) => ({
        cause_id: cause.cause_id || `cause_${Date.now()}_${Math.random()}`,
        cause_name: cause.name_localized || cause.cause_name || 'Unknown cause',
        cause_suggestion: cause.suggestion_localized || cause.cause_suggestion || '',
        explanation: cause.explanation_localized || cause.explanation || ''
      }));
      
    case 'symptoms':
      return completeItems.map((symptom: any) => ({
        symptom_id: symptom.symptom_id || `symptom_${Date.now()}_${Math.random()}`,
        symptom_name: symptom.name_localized || symptom.symptom_name || 'Unknown symptom',
        symptom_suggestion: symptom.suggestion_localized || symptom.symptom_suggestion || '',
        explanation: symptom.explanation_localized || symptom.explanation || ''
      }));
      
    case 'properties':
      return completeItems.map((property: any) => ({
        property_id: property.property_id || `property_${Date.now()}_${Math.random()}`,
        property_name: property.property_name_localized || property.property_name || 'Unknown property',
        property_name_english: property.property_name_english || property.property_name || 'Unknown property',
        property_name_localized: property.property_name_localized || property.property_name || 'Unknown property',
        description: property.description_contextual_localized || property.description_localized || property.description || '',
        description_localized: property.description_contextual_localized || property.description_localized || '',
        description_contextual_localized: property.description_contextual_localized || '',
        relevancy: property.relevancy_score || property.relevancy || 0,
        relevancy_score: property.relevancy_score || 0,
        addresses_cause_ids: property.addresses_cause_ids || [],
        addresses_symptom_ids: property.addresses_symptom_ids || [],
        suggested_oils: property.suggested_oils || []
      }));
      
    default:
      console.warn('Unknown step type for transformation:', stepType);
      return completeItems;
  }
}

/**
 * Processes final streaming data and extracts the relevant array
 * Eliminates redundant final data processing logic in components
 * @param finalData Raw final data from streaming
 * @param jsonArrayPath Path to the data array (e.g., 'data.potential_causes')
 * @returns Extracted array data
 */
export function extractFinalStreamingData(finalData: any, jsonArrayPath: string): any[] {
  if (!finalData) {
    return [];
  }

  console.log('🔍 Extracting final streaming data from path:', jsonArrayPath);

  // Handle direct array response
  if (Array.isArray(finalData)) {
    return finalData;
  }

  // Handle nested object response
  if (finalData && typeof finalData === 'object') {
    const extractedData = getNestedProperty(jsonArrayPath, finalData);
    if (Array.isArray(extractedData)) {
      return extractedData;
    }
  }

  console.warn('Could not extract array data from final response');
  return [];
} 