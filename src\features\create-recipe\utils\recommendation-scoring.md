# Recommendation Scoring System

## Overview

The `recommendation-scoring.ts` module implements a sophisticated scoring system for essential oil recommendations. It processes normalized recommendation data and enriches it with calculated scores to create a final ranked list of oil recommendations based on their comprehensive ability to address the user's specific symptoms and causes.

## Core Concepts

### Data Flow Pipeline

1. **Input**: Normalized recommendation data with therapeutic properties and oils
2. **Enrichment**: Add calculated scores to properties and oil suggestions
3. **Aggregation**: Sum scores across properties for each unique oil
4. **Output**: Sorted list of oils ranked by final relevance score

## Calculated Fields

### 1. `property_coverage_score` (0-1 scale)

**Purpose**: Normalizes how relevant a therapeutic property is to the user's specific problem.

**Calculation**: 
```typescript
property.relevancy_score / MAX_RELEVANCY_SCORE
```
Where `MAX_RELEVANCY_SCORE = 5`

**Meaning**: 
- Score closer to 1.0 = Property is highly relevant to user's symptoms/causes
- Score closer to 0.0 = Property is less relevant

**Example**: 
- Property with `relevancy_score: 4` → `property_coverage_score: 0.8`
- Property with `relevancy_score: 2` → `property_coverage_score: 0.4`

### 2. `recommendation_instance_score` (variable scale)

**Purpose**: Scores a specific oil recommendation within a property's context.

**Calculation**:
```typescript
relevancy_to_property_score × relevancy_score × property_coverage_score
```

**Meaning**: Combines the oil's relevance to the property with the property's overall relevance to create a contextual score.

**Example**:
- Oil has `relevancy_to_property_score: 0.9`
- Property has `relevancy_score: 4` and `property_coverage_score: 0.8`
- Instance score = `0.9 × 4 × 0.8 = 2.88`

### 3. `final_relevance_score` (0+ scale, rounded to 2 decimals)

**Purpose**: Aggregates all instance scores for each unique oil across all properties.

**Calculation**: Sum of all `recommendation_instance_score` values for that oil across all therapeutic properties.

**Meaning**: Total relevance score indicating how well an oil addresses the user's complete problem profile.

**Example**:
- Oil A appears in 3 properties with instance scores: 2.5, 1.8, 0.9
- `final_relevance_score = 5.2`

## Key Functions

### `calculatePropertyCoverageScore(property)`
- Normalizes property relevancy to 0-1 scale
- Used as a weighting factor in instance score calculations

### `calculateRecommendationInstanceScore(oilSuggestion, property)`
- Calculates contextual score for oil within property
- Multiplies oil relevance by property relevance and coverage

### `enrichRecommendationDataWithScores(recommendationData)`
- Main enrichment function
- Adds coverage scores to properties
- Adds instance scores to oil suggestions
- Filters to only include enriched oils

### `calculateFinalRankedList(enrichedRecommendationData)`
- Aggregates instance scores across all properties
- Creates final ranked list sorted by relevance
- Rounds scores to 2 decimal places

## Data Structures

### `EnrichedTherapeuticProperty`
```typescript
interface EnrichedTherapeuticProperty {
  // ... original TherapeuticProperty fields
  property_coverage_score: number;
  suggested_oils: EnrichedOilSuggestion[];
  isEnriched: boolean;
}
```

### `EnrichedOilSuggestion`
```typescript
interface EnrichedOilSuggestion {
  oil_id: string;
  match_rationale_localized: string;
  relevancy_to_property_score: number;
  recommendation_instance_score: number;
}
```

### `FinalRankedOil`
```typescript
interface FinalRankedOil {
  oil_id: string;
  name_localized: string;
  final_relevance_score: number;
}
```

## Usage Example

```typescript
// 1. Start with normalized data
const normalizedData = {
  therapeuticProperties: [...],
  oils: [...]
};

// 2. Enrich with calculated scores
const enrichedData = enrichRecommendationDataWithScores(normalizedData);

// 3. Generate final ranked list
const finalRankedOils = calculateFinalRankedList(enrichedData);

// 4. Use ranked list for recommendations
console.log(finalRankedOils[0]); // Highest scoring oil
```

## Benefits

### Contextual Scoring
- Each oil gets scored based on its specific role in addressing the user's problem
- Considers both individual oil properties and overall problem relevance

### Property Weighting
- More relevant properties contribute more to the final oil scores
- Ensures recommendations prioritize the most important therapeutic effects

### Deduplication
- Oils appearing in multiple properties get their scores aggregated
- Prevents duplicate recommendations while maintaining comprehensive scoring

### Ranking
- Final scores enable clear prioritization of recommendations
- Users can see which oils are most effective for their specific situation

## Scoring Philosophy

The system prioritizes oils that:
1. **Address multiple aspects** of the user's problem (higher aggregation scores)
2. **Are highly relevant** to the most important therapeutic properties
3. **Have strong individual properties** within their therapeutic contexts

This ensures recommendations are both comprehensive and targeted to the user's specific needs.
