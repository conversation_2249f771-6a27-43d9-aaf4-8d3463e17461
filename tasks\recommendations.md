# Recommendations for Final Recipe Step Implementation

This document provides additional recommendations and clarifications for the tasks outlined in `tasks-prd-create-recipe-final-step.md`. These suggestions are based on a review of the existing codebase and the new requirements from `prd-create-recipe-final-step.md`.

## 1. Enhanced Type Safety with Zod

While the PRD includes the creation of Zod schemas for AI response validation, we recommend extending the use of Zod to all data transformations within the "Create Recipe" feature.

- **Recommendation**: Create a central file for all Zod schemas related to the recipe creation process, not just the final step. This will ensure that data is validated at each step of the wizard, preventing inconsistencies and runtime errors.
- **Action**: Create `src/features/create-recipe/schemas/recipe.schemas.ts` and define schemas for `HealthConcernData`, `DemographicsData`, `PotentialCause`, `PotentialSymptom`, and `TherapeuticProperty`. Use these schemas to validate data as it is saved to the Zustand store.

## 2. Robust State Machine for Final Recipe Generation

The generation of three recipe protocols in parallel is a complex asynchronous operation. A simple loading flag will not be sufficient to represent the different states of this process.

- **Recommendation**: Implement a more granular state machine for the `finalRecipes` state in `recipe.store.ts`. This will allow the UI to accurately reflect the status of each individual recipe protocol (morning, mid-day, night), as required by **FR-17**.
- **Action**: Modify the `FinalRecipesState` interface in `recipe.types.ts` to include a map or object for the generation status of each recipe:

  ```typescript
  interface FinalRecipeStatus {
    status: 'idle' | 'loading' | 'success' | 'error';
    error?: string | null;
  }

  interface FinalRecipesState {
    recipeProtocols: {
      morning: FinalRecipeProtocol | null;
      mid-day: FinalRecipeProtocol | null;
      night: FinalRecipeProtocol | null;
    };
    generationStatus: {
      morning: FinalRecipeStatus;
      mid-day: FinalRecipeStatus;
      night: FinalRecipeStatus;
    };
    // ... other state properties
  }
  ```

## 3. Clear Component Hierarchy and Data Flow

To maintain a clean and scalable architecture, it's important to define a clear hierarchy and data flow for the new components, as per **Design Consideration 6.3**.

- **Recommendation**: Structure the components with a clear separation of concerns. The main `FinalRecipesDisplay` component should be responsible for fetching data and managing state, while the child components should be presentational.
- **Action**:
    - `FinalRecipesDisplay.tsx`: Fetches data from the `useRecipeStore` and the `use-final-recipes-generation` hook. It will manage the overall layout and pass the relevant data down to the `RecipeProtocolCard` components.
    - `RecipeProtocolCard.tsx`: Receives a single recipe protocol and its generation status as props. It will be responsible for displaying the recipe information and handling UI interactions like expanding and collapsing sections.
    - `ContainerRecommendation.tsx` and `SafetyWarnings.tsx`: These should be pure presentational components that receive their data as props from `RecipeProtocolCard.tsx`.

## 4. Detailed Error Handling and User Feedback

A robust error handling strategy is crucial for a good user experience, especially when dealing with multiple parallel API calls, as stated in **FR-16** and **FR-17**.

- **Recommendation**: Provide specific and actionable error messages to the user for different failure scenarios.
- **Action**:
    - In `use-final-recipes-generation.ts`, catch errors for each AI call individually.
    - Update the `generationStatus` in the Zustand store with specific error messages.
    - In `FinalRecipesDisplay.tsx`, display a unique error message for each failed recipe protocol. The PRD states that regeneration is out of scope, so a "Retry" button should not be implemented.
    - Implement a global error message if all three recipe generations fail.

## 5. Internationalization (i18n) Strategy for Dynamic Content

The PRD mentions i18n (**Success Metric 8.4**), and it's important to have a clear strategy for handling dynamic content from the AI.

- **Recommendation**: Prompt the AI to provide localized strings for all user-facing content. When this is not possible, ensure that all static text is properly internationalized.
- **Action**:
    - In `final-recipes.yaml`, instruct the AI to provide localized names and descriptions for recipes, ingredients, and safety warnings.
    - For oil names, continue the existing pattern of displaying both the localized and botanical names.
    - Ensure that all UI components in the final step use the `useTranslation` hook from `react-i18next` for all static text.