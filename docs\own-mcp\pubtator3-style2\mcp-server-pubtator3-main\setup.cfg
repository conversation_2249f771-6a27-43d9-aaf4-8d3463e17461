[metadata]
name = mcp-server-pubtator3
version = 0.2.1
description = PubTator3 API compatible with the MCP agent protocol
author = <PERSON><PERSON>
author_email = <EMAIL>
license = MIT AND (Apache-2.0 OR BSD-2-Clause)
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/QIngyuanfl/mcp-server-pubtator3.git

[options]
packages = find:
package_dir =
    =src
python_requires = >=3.10
install_requires =
    aiohttp
    aiolimiter
    mcp[cli]

[options.packages.find]
where = src

