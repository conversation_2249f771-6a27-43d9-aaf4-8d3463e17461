'use server';

import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import { getServerLogger } from '@/lib/logger';
import { cache } from 'react';

const logger = getServerLogger('ServerAuthUtils');

export interface ServerAuthResult {
  user: any;
  dehydratedState: any;
  error?: Error;
}

/**
 * Centralized server-side auth and profile prefetching utility
 * Uses existing cached services to follow DRY principles
 * 
 * This utility combines:
 * - Auth state retrieval (already cached)
 * - Profile prefetching with timeout protection
 * - Query client state dehydration
 * 
 * Used by layouts to prevent code duplication
 */
export const getServerAuthWithProfilePrefetch = cache(async (
  options: {
    prefetchProfile?: boolean;
    profileTimeout?: number;
    profileStaleTime?: number;
    requestId?: string;
    debugMode?: boolean;
  } = {}
): Promise<ServerAuthResult> => {
  const {
    prefetchProfile = true,
    profileTimeout = 500,
    profileStaleTime = 10 * 1000,
    requestId = `req-${Date.now()}`,
    debugMode = false
  } = options;

  // Use existing cached auth state service
  const { user, error: authError } = await getServerAuthState();

  if (authError) {
    logger.error('ServerAuthUtils: Error fetching user from auth service.', { 
      error: authError.message,
      requestId 
    });
    
    // Return empty query client state even on auth error
    const queryClient = new QueryClient();
    return {
      user: null,
      dehydratedState: dehydrate(queryClient),
      error: authError
    };
  }

  const queryClient = new QueryClient();

  if (user && prefetchProfile) {
    if (debugMode) {
      console.log(`[${new Date().toISOString()}] ServerAuthUtils: Prefetching profile for user (${requestId})`);
    }
    
    try {
      // Race prefetch against timeout to prevent blocking rendering
      // Uses existing cached profile service (already implements React cache())
      const profilePromise = queryClient.prefetchQuery({
        queryKey: ['userProfile', user.id],
        queryFn: () => getCurrentUserProfile(user.id),
        staleTime: profileStaleTime,
      });

      const timeoutPromise = new Promise(resolve => setTimeout(resolve, profileTimeout));
      await Promise.race([profilePromise, timeoutPromise]);

      if (debugMode) {
        console.log(`[${new Date().toISOString()}] ServerAuthUtils: Profile prefetch completed (${requestId})`);
      }
    } catch (error) {
      const castError = error instanceof Error ? error : new Error(String(error));
      logger.error('ServerAuthUtils: Failed to prefetch user profile.', { 
        userId: user.id.substring(0, 6) + '...', 
        error: castError.message,
        requestId
      });
      // Continue even if prefetch fails - client-side will attempt to fetch
    }
  } else if (!user) {
    logger.info('ServerAuthUtils: No authenticated user found. Skipping profile prefetch.', {
      requestId
    });
  }

  return {
    user,
    dehydratedState: dehydrate(queryClient),
    error: authError
  };
}); 