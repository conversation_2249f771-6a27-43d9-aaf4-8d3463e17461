I need you to follow the exact process specified in `docs/ai-dev-tasks/create-prd.mdc` to create a proper PRD for the create-recipe final step feature. A previous developer created `tasks/prd-create-recipe-final-step.md` but skipped the mandatory clarifying questions step outlined in the process.

Please follow these specific steps:

1. **First, read and understand these three files:**
   - `docs/ai-dev-tasks/create-prd.mdc` (the PRD creation process and template)
   - `docs/create-recipe/new-final-step/getting-ready/brainstorm.md` (the feature requirements)
   - `tasks/prd-create-recipe-final-step-v1.md` (the incorrectly created PRD to understand what was missed)
   - `tasks/prd-create-recipe-final-step-v2.md` (the incorrectly created PRD to understand what was missed)

2. **Follow the exact process from `docs/ai-dev-tasks/create-prd.mdc`:**
   - Step 1: Receive the initial prompt (the brainstorm document serves as this)
   - Step 2: **Ask clarifying questions** (this step was skipped - you must do this)
   - Step 3: Generate the PRD based on my answers to your clarifying questions
   - Step 4: Save the PRD as specified in the template

3. **Important requirements:**
   - You MUST ask clarifying questions before creating any PRD (as specified in the process)
   - Use the question examples provided in the template but adapt them to this specific feature
   - Focus on areas where the brainstorm document may be unclear or incomplete
   - Do NOT proceed to create the PRD until I have answered your clarifying questions
   - Follow the exact PRD structure outlined in the template

The goal is to create a more comprehensive and accurate PRD by following the proper discovery process through clarifying questions, rather than making assumptions based solely on the brainstorm document.

Some Clarifying Questions that might get you ahead in order to make new ones:


   1. User Interaction & Finality:
       * The existing PRD asks if users should be able to regenerate recipes. The brainstorm doesn't       
         specify. Is the recipe generation a one-time event for a given user session, or should users be   
         able to trigger a regeneration? No, users cannot regenerate recipes because it takes a lot of Ai tokens to generate one. It is very costly.
       * Similarly, should users be able to edit the final generated recipes (e.g., change an oil, adjust a
         quantity) before they consider them "final"? Yes, that is a possibility, but lets add that in Phase 2, not on this first roll out.


   2. "Root Cause Analysis" and "Therapeutic Strategy":
       * The documents mention displaying a "root cause analysis" and "therapeutic strategy rationale." Could
          you clarify what level of detail is expected here? Is this a short paragraph, a list of bullet     
         points, or something more complex? Is there a specific tone or style it should follow (e.g.,        
         clinical, holistic)? The original dataset already has a rationale for each oil and each property that is available on the dataset. The AI prompt must build on top of it, it is a really short sentence only to the user understand why that oil was selected. OIL "rationale": "A lavanda é amplamente reconhecida por suas propriedades calmantes e relaxantes, ajudando a aliviar a tensão muscular na cabeça e no pescoço e promovendo um sono reparador." Property Rationale "desc": "Ajuda a aliviar a tensão muscular na cabeça e pescoço, reduzindo a frequência e intensidade das dores de cabeça relacionadas ao estresse e à tensão.",




   3. Error Handling Specifics:
       * The brainstorm mentions "graceful fallbacks for AI generation failures." The PRD asks what should
         happen. Could you be more specific? If one of the three parallel recipe generations fails (e.g., 
         the "Night" recipe), should we:
           * Show an error for that specific section and display the two successful recipes? Yes, display the success full ones but it should handle retries for the failed ones.
           * Show a global error and display no recipes at all? No, display the success full ones but it should handle retries for the failed ones.
           * Automatically retry the failed generation twice? Yes


   4. Data Persistence:
       * The existing PRD brings up a good question about state management. Should the final, generated
         recipe data be saved to the database for the user to view later, or is it only for in-session 
         viewing and disappears once they close the browser? For this moment it is just a in-session viewing. Add a saving option to phase 2


   5. UI/UX Details:
       * The standalone-v1.html mockup is a key reference. Are all the fields shown in that mockup        
         considered mandatory for the MVP? Yes. All the data are mandatory. Only the 'segurança' tab has fixed data, all other tabs are dinamycally fed.
       * The brainstorm mentions "Container & Dispensing System recommendations." How specific should this
         be? Should it recommend a specific size in ml/oz (e.g., "10ml roller bottle") or just the type   
         (e.g., "Roller bottle")? It should size, container type, and dispenser type. A recipe can have a infinite variation of it. Spray 60ml bottle | Roll-on 10ml bottle | 30ml dropper bottle

Container System (The Bottle & Dispenser)
*Select the ideal packaging based on the recipe and its intended use.*
*   **Container Selection**
    *   📌 **Size Calculation:** Based on usage frequency, duration, and application method.
    *   **Material & UV Protection:** Choose appropriate glass color, plastic, or stainless steel.
*   **Dispensing System**
    *   📌 **Select the best dispenser:** Dropper bottles, Roller balls, Spray bottles, Pump dispensers, Solid containers (tins/jars), or personal Inhalers.

    Examples of rationale to decide container size
    Daily use + face application = 10-15ml roller bottle
    Weekly use + body application = 30ml dropper bottle
    Room spray = 100ml spray bottle

    6. AI Model and Performance Specifications
Model Selection: The YAML prompt template specifies gpt-4.1-turbo, but your memories mention gpt-4.1-nano as a valid model. Which specific model should be used for this feature? gpt-4.1-nano
Token Management: Given the cost concerns you mentioned about regeneration, what are the expected token limits per recipe generation? Should there be any token optimization strategies? No. 
Parallel Processing: Should all 3 recipes (morning/mid-day/night) use the same AI model and temperature settings, or should they have different configurations (e.g., more energizing prompts for morning)? Yes, same prompt for all. The time of the day will be passed as a variable when it triggers the AI. Besides all the information gathered during the flow, each of the 3 recipes will trigger with a different variable for this time-day variable so IT can be used in the prompt.

7. Safety Implementation Details
Child Age Thresholds: At what specific age should the dermocaustic oil filtering kick in? Is it anyone under 18, or a different threshold? The multistep flow has a age category and age specific variable that it is selected on the first steps. If category and age is under 10 years old, it should have a special filter.
Safety Data Source: The JSON dataset shows safety IDs - should the system reference the existing safety database, or will safety rules be hardcoded in the AI prompt? The safety will be dynamically fed on the AI prompt such as the causes, symptons and oils are being populated in another steps.
Pregnancy/Nursing: How should the system handle pregnancy/nursing safety warnings if that demographic data is collected? No difference. It is jsut regular data that is nested inside each oil.
8. Container Recommendation Logic
Calculation Basis: You mentioned examples like "Daily use + face application = 10-15ml roller bottle." Should the AI determine container size based on:
Number of drops per application × frequency × duration? yes. it is not an exact calculation, but estimate. If application is on the back, or in the legs, the size should be bigger for applications on larger body parts. Example: face vs back or legs. Feet vs Torax.
Specific application area (face vs body)? Sure
Both factors combined? Yes.
Material Preferences: Should the system recommend specific materials (amber glass, clear glass, plastic) based on oil properties or user preferences? Based on oils/synergy conservation
9. Workflow Integration Specifics
Step Replacement: When replacing the "oils" step, should the step numbering change (e.g., from 6 steps to 5 steps), or should it remain as "Step 6" but with new functionality? Make it sequential. Take this decision on your own.
Navigation Behavior: If a user navigates back from the final step to modify earlier inputs, should the generated recipes be cleared, or should they persist until new generation is triggered? It is not allowed to go back fron the final step. All variables were already defined and cannot be changed since it is expensive to generate new recipes.
Data Validation: What should happen if the user reaches the final step but some required data from previous steps is missing or invalid? This is impossible. The data is already at the DEV only button at the overlay in the properties step.
10. Internationalization and Localization
Recipe Timing: You mentioned potential cultural considerations for timing. Should "morning/mid-day/night" be translated literally, or should there be cultural adaptations (e.g., siesta considerations for Spanish users)? All face user text should be generated with keys _localized since we now the language of the user that it is define since the first step of the multi-step flow.
Oil Names: Should oil names be displayed in the user's language, botanical names, or both? The dataset shows both Portuguese and English names. - It should displayed localized and botanical names.
Measurement Units: Should the system support different measurement units (ml vs fl oz) based on locale? no... only internacional measurements. Drops and ml
11. Technical Architecture Details
Streaming vs Batch: Should the three recipes be generated with streaming responses (showing progress) or as batch responses (all at once)? Streaming responses if we want to implementing a loading feature. The loading feature would be done only on phase2, but we should already use the streaming since the first implementation.
State Management: Should the final step data be stored in the same Zustand store as other steps, or should it have its own dedicated store slice? I am not sure. Take this decision please. The final step will use data from the previous steps such oils id to get safety information, properties id to get full description, etc...
Component Structure: Should this be one large component or broken down into smaller components (e.g., RecipeCard, ContainerRecommendation, SafetyWarnings)? Each file should not be bigger than 500 lines of code, and follow the DRY concept, also KISS concept, so you should take this decision on how to factor and modularize this step.
12. Testing and Validation Requirements
Test Data: Should the system include mock/test data for development and testing, or should it always require real AI responses? It can include mock/test data since generationg recipe with ai takes a lot. The input that would be this format docs\create-recipe\new-final-step\json-dataset-minimal.json + variable of the time of the day
Validation Rules: What validation should be applied to AI-generated responses to ensure they contain all required fields and safe recommendations? It should validade the json schema that would be created in the YAML prompt file for this step.