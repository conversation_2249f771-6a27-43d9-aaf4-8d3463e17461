I need you to follow the exact process specified in `docs/ai-dev-tasks/create-prd.mdc` to create a proper PRD for the create-recipe final step feature. A previous developer created `tasks/prd-create-recipe-final-step.md` but skipped the mandatory clarifying questions step outlined in the process.

Please follow these specific steps:

1. **First, read and understand these three files:**
   - `docs/ai-dev-tasks/create-prd.mdc` (the PRD creation process and template)
   - `docs/create-recipe/new-final-step/getting-ready/brainstorm.md` (the feature requirements)
   - `tasks/prd-create-recipe-final-step.md` (the incorrectly created PRD to understand what was missed)

2. **Follow the exact process from `docs/ai-dev-tasks/create-prd.mdc`:**
   - Step 1: Receive the initial prompt (the brainstorm document serves as this)
   - Step 2: **Ask clarifying questions** (this step was skipped - you must do this)
   - Step 3: Generate the PRD based on my answers to your clarifying questions
   - Step 4: Save the PRD as specified in the template

3. **Important requirements:**
   - You MUST ask clarifying questions before creating any PRD (as specified in the process)
   - Use the question examples provided in the template but adapt them to this specific feature
   - Focus on areas where the brainstorm document may be unclear or incomplete
   - Do NOT proceed to create the PRD until I have answered your clarifying questions
   - Follow the exact PRD structure outlined in the template

The goal is to create a more comprehensive and accurate PRD by following the proper discovery process through clarifying questions, rather than making assumptions based solely on the brainstorm document.

Some Clarifying Questions that might get you ahead in order to make new ones:


   1. User Interaction & Finality:
       * The existing PRD asks if users should be able to regenerate recipes. The brainstorm doesn't       
         specify. Is the recipe generation a one-time event for a given user session, or should users be   
         able to trigger a regeneration? No, users cannot regenerate recipes because it takes a lot of Ai tokens to generate one. It is very costly.
       * Similarly, should users be able to edit the final generated recipes (e.g., change an oil, adjust a
         quantity) before they consider them "final"? Yes, that is a possibility, but lets add that in Phase 2, not on this first roll out.


   2. "Root Cause Analysis" and "Therapeutic Strategy":
       * The documents mention displaying a "root cause analysis" and "therapeutic strategy rationale." Could
          you clarify what level of detail is expected here? Is this a short paragraph, a list of bullet     
         points, or something more complex? Is there a specific tone or style it should follow (e.g.,        
         clinical, holistic)? The original dataset already has a rationale for each oil and each property that is available on the dataset. The AI prompt must build on top of it, it is a really short sentence only to the user understand why that oil was selected. OIL "rationale": "A lavanda é amplamente reconhecida por suas propriedades calmantes e relaxantes, ajudando a aliviar a tensão muscular na cabeça e no pescoço e promovendo um sono reparador." Property Rationale "desc": "Ajuda a aliviar a tensão muscular na cabeça e pescoço, reduzindo a frequência e intensidade das dores de cabeça relacionadas ao estresse e à tensão.",




   3. Error Handling Specifics:
       * The brainstorm mentions "graceful fallbacks for AI generation failures." The PRD asks what should
         happen. Could you be more specific? If one of the three parallel recipe generations fails (e.g., 
         the "Night" recipe), should we:
           * Show an error for that specific section and display the two successful recipes? Yes, display the success full ones but it should handle retries for the failed ones.
           * Show a global error and display no recipes at all? No, display the success full ones but it should handle retries for the failed ones.
           * Automatically retry the failed generation twice? Yes


   4. Data Persistence:
       * The existing PRD brings up a good question about state management. Should the final, generated
         recipe data be saved to the database for the user to view later, or is it only for in-session 
         viewing and disappears once they close the browser? For this moment it is just a in-session viewing. Add a saving option to phase 2


   5. UI/UX Details:
       * The standalone-v1.html mockup is a key reference. Are all the fields shown in that mockup        
         considered mandatory for the MVP? Yes. All the data are mandatory. Only the 'segurança' tab has fixed data, all other tabs are dinamycally fed.
       * The brainstorm mentions "Container & Dispensing System recommendations." How specific should this
         be? Should it recommend a specific size in ml/oz (e.g., "10ml roller bottle") or just the type   
         (e.g., "Roller bottle")? It should size, container type, and dispenser type. A recipe can have a infinite variation of it. Spray 60ml bottle | Roll-on 10ml bottle | 30ml dropper bottle

Container System (The Bottle & Dispenser)
*Select the ideal packaging based on the recipe and its intended use.*
*   **Container Selection**
    *   📌 **Size Calculation:** Based on usage frequency, duration, and application method.
    *   **Material & UV Protection:** Choose appropriate glass color, plastic, or stainless steel.
*   **Dispensing System**
    *   📌 **Select the best dispenser:** Dropper bottles, Roller balls, Spray bottles, Pump dispensers, Solid containers (tins/jars), or personal Inhalers.

    Examples of rationale to decide container size
    Daily use + face application = 10-15ml roller bottle
    Weekly use + body application = 30ml dropper bottle
    Room spray = 100ml spray bottle