---
type: "agent_requested"
---

# API Helpers Usage Rules

## Required Imports
```typescript
import { 
  validateOpenA<PERSON><PERSON><PERSON>,
  parseJsonBody,
  createErrorResponse,
  createSuccessResponse,
  withErrorHandling
} from '../utils/api-helpers';
```

## API Routes (route.ts)
1. Always wrap handler with `withErrorHandling`
2. Use `parseJsonBody` for request parsing
3. Use `createErrorResponse` and `createSuccessResponse`
4. Validate API keys at route start
```typescript
// ✅ Correct API Route Pattern
export const POST = withErrorHandling(async (request) => {
  validateOpenAIKey();
  const body = await parseJsonBody(request);
  const result = await service.process(body);
  return createSuccessResponse(result);
});
```

## Services (service.ts)
1. Don't use response creators
2. Throw descriptive errors
3. Log detailed errors for debugging
```typescript
// ✅ Correct Service Pattern
try {
  // ... service logic
} catch (error) {
  console.error('[ServiceName] Operation failed:', error);
  throw new Error('Clear error description');
}
```

## Error Messages
- Routes: HTTP-friendly messages
- Services: Descriptive internal errors
- Never expose internal details to client 