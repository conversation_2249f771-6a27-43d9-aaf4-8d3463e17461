---
type: "manual"
---

# i18n Implementation Guidelines

## Overview
This project uses a namespace-based i18n system with comprehensive server-side support for English (en), Portuguese (pt), and Spanish (es).

## Language Handling

### Recommended Pattern

```typescript
import { useApiLanguage } from '@/lib/i18n/utils';
import { useI18n } from '@/hooks/use-i18n';

export function MyComponent() {
  const apiLanguage = useApiLanguage();
  const { t } = useI18n();
  // ... use apiLanguage for API calls and t() for UI translations
}
```

### Why This Pattern?

1. **Efficiency**: `useApiLanguage` internally handles both getting the user's language preference and converting it to the API format in one step
2. **Clarity**: Provides the API-formatted language string directly, reducing potential formatting mistakes
3. **Consistency**: Ensures uniform language handling across all components
4. **DRY (Don't Repeat Yourself)**: Eliminates the need to manually convert user language to API format in each component
5. **Type Safety**: Returns the correct API language format ('EN_US', 'PT_BR', etc.) directly

### What to Avoid

```typescript
// ❌ Don't do this:
import { useUserLanguage, getApiLanguage } from '@/lib/i18n/utils';

const userLanguage = useUserLanguage();
const apiLanguage = getApiLanguage(userLanguage);
```

This approach is more verbose and introduces unnecessary intermediate steps.

## Translation Usage

1. Use the `useI18n` hook for UI translations:
   ```typescript
   const { t } = useI18n();
   ```

2. Use namespace-prefixed keys with correct case:
   ```typescript
   // ✅ Good - Use kebab-case for step keys
   t('create-recipe:steps.health-concern.title')
   t('create-recipe:steps.demographics.title')
   t('common:buttons.continue')

   // ❌ Bad - Wrong case for step keys
   t('create-recipe:steps.healthConcern.title')
   t('create-recipe:healthConcern.title')
   t('healthConcern.title', { ns: 'create-recipe' })
   ```

3. Pass variables as the third argument:
   ```typescript
   // ✅ Good
   t('common:status.lastSaved', undefined, { time: lastSaved })
   
   // ❌ Bad
   t('common:status.lastSaved', { time: lastSaved })
   ```

## Translation Key Naming Conventions

### CRITICAL: Step Key Case Sensitivity
Step keys MUST use kebab-case to match `RecipeStep` enum values:

```typescript
// ✅ CORRECT - Matches enum values
enum RecipeStep {
  HEALTH_CONCERN = 'health-concern',
  DEMOGRAPHICS = 'demographics',
  CAUSES = 'causes'
}

// Use in translations
t('create-recipe:steps.health-concern.title')
t('create-recipe:steps.demographics.title')

// ❌ WRONG - Will cause translation failures
t('create-recipe:steps.healthConcern.title')
t('create-recipe:steps.HealthConcern.title')
```

### Server-side Metadata Internationalization
For page metadata, use server-side utilities:

```typescript
import { getServerLocale, getLocalizedMetadata } from '@/lib/i18n/utils/server-language-utils';

export async function generateMetadata() {
  const locale = await getServerLocale();
  const metadata = getLocalizedMetadata(locale);

  return {
    title: metadata.recipeCreator,
    description: metadata.description
  };
}
```

## Language Files

### File Organization
```
src/lib/i18n/messages/
├── en/                    # English (base language)
│   ├── common.json        # Shared UI elements
│   ├── auth.json          # Authentication flows
│   ├── dashboard.json     # Dashboard navigation
│   ├── create-recipe.json # Recipe creation wizard
│   └── homepage.json      # Public marketing content
├── pt/                    # Portuguese (Brazil)
│   └── (identical structure with translations)
└── es/                    # Spanish (Latin America)
    └── (identical structure with translations)
```

### Translation Quality Requirements
1. **100% key consistency**: All language files must have equivalent keys
2. **No fallback dependencies**: Remove fallback text in production code
3. **Namespace compliance**: All features must use proper namespace syntax
4. **Case sensitivity**: Step keys must use kebab-case consistently
5. **Server-side support**: Use server utilities for metadata internationalization

### Troubleshooting Translation Issues
1. **English text in Portuguese/Spanish**: Check for missing translation keys
2. **Key not found errors**: Verify key exists in all language files
3. **Case mismatch**: Ensure step keys use kebab-case format
4. **Server-side issues**: Use `getServerLocale()` for locale detection

